"""
混沌测试批次任务数据访问层
"""
import logging
from typing import List, Optional, Tuple, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func, delete
from sqlalchemy.orm import selectinload

logger = logging.getLogger(__name__)

from app.models.chaos.chaos_batch_task import ChaosBatchTask, ChaosBatchTaskItem
from app.repositories.base import BaseRepository
from app.schemas.chaos.chaos_batch_task import (
    ChaosBatchTaskCreate, ChaosBatchTaskUpdate, ChaosBatchTaskQuery,ChaosBatchTaskItemCreate
)

logger = logging.getLogger(__name__)


class ChaosBatchTaskRepository(BaseRepository[ChaosBatchTask, ChaosBatchTaskCreate, ChaosBatchTaskUpdate]):
    """
    批次任务数据访问层
    专注于批次任务相关的数据库操作
    """

    def __init__(self, db: AsyncSession):
        super().__init__(ChaosBatchTask, db)

    async def get_with_items(self, task_id: int) -> Optional[ChaosBatchTask]:
        """获取批次任务及其子任务"""
        query = (
            select(ChaosBatchTask)
            .options(selectinload(ChaosBatchTask.task_items))
            .where(ChaosBatchTask.id == task_id)
        )
        result = await self.db.execute(query)
        return result.scalar_one_or_none()

    async def get_with_executions(self, task_id: int) -> Optional[ChaosBatchTask]:
        """获取批次任务及其执行记录"""
        query = (
            select(ChaosBatchTask)
            .options(
                selectinload(ChaosBatchTask.task_items),
                selectinload(ChaosBatchTask.executions)
            )
            .where(ChaosBatchTask.id == task_id)
        )
        result = await self.db.execute(query)
        return result.scalar_one_or_none()

    async def create_with_items(self, create_data: ChaosBatchTaskCreate, created_by: int) -> ChaosBatchTask:
        """创建批次任务及其子任务"""
        # 创建主任务
        task_data = create_data.model_dump(exclude={'task_items'})
        task = ChaosBatchTask(**task_data)
        task.created_by = created_by
        
        self.db.add(task)
        await self.db.flush()  # 获取主任务ID
        
        # 创建子任务
        for item_data in create_data.task_items:
            item = ChaosBatchTaskItem(
                batch_task_id=task.id,
                **item_data.model_dump()
            )
            self.db.add(item)
        
        await self.db.commit()
        await self.db.refresh(task)
        
        # 重新查询以获取完整的关联数据
        return await self.get_with_items(task.id)

    async def update_with_items(
        self, 
        task_id: int, 
        update_data: ChaosBatchTaskUpdate, 
        updated_by: int
    ) -> Optional[ChaosBatchTask]:
        """更新批次任务及其子任务"""
        task = await self.get_with_items(task_id)
        if not task:
            return None
        
        # 更新主任务
        task_data = update_data.model_dump(exclude={'task_items'}, exclude_unset=True)
        for key, value in task_data.items():
            setattr(task, key, value)
        task.updated_by = updated_by
        
        # 如果提供了子任务数据，则更新子任务
        if update_data.task_items is not None:
            # 智能更新子任务：考虑外键约束
            await self._update_task_items_safely(task, update_data.task_items)
        
        await self.db.commit()
        await self.db.refresh(task)
        
        # 重新查询以获取完整的关联数据
        return await self.get_with_items(task.id)

    async def _update_task_items_safely(
        self,
        task: ChaosBatchTask,
        new_items: List[ChaosBatchTaskItemCreate]
    ) -> None:
        """安全地更新子任务，考虑外键约束"""
        from sqlalchemy import select, exists
        from app.models.chaos.chaos_execution import ChaosExecution

        # 获取现有子任务
        existing_items = {item.id: item for item in task.task_items}

        # 处理新的子任务数据
        new_items_by_id = {}
        items_to_create = []

        for item_data in new_items:
            item_dict = item_data.model_dump()
            item_id = item_dict.get('id')

            if item_id and item_id in existing_items:
                # 有ID且存在于现有任务中，是更新
                new_items_by_id[item_id] = item_dict
            else:
                # 没有ID或ID不存在于现有任务中，是新建
                # 移除ID字段避免创建时的冲突
                if 'id' in item_dict:
                    del item_dict['id']
                items_to_create.append(item_dict)

        # 更新现有子任务
        for item_id, item_data in new_items_by_id.items():
            if item_id in existing_items:
                existing_item = existing_items[item_id]
                # 更新字段
                for key, value in item_data.items():
                    if key != 'id':  # 不更新ID
                        setattr(existing_item, key, value)

        # 创建新子任务
        for item_data in items_to_create:
            item = ChaosBatchTaskItem(
                batch_task_id=task.id,
                **item_data
            )
            self.db.add(item)

        # 删除不再需要的子任务（只删除没有执行记录的）
        items_to_keep = set(new_items_by_id.keys())
        for item_id, existing_item in existing_items.items():
            if item_id not in items_to_keep:
                # 检查是否有执行记录
                has_executions = await self.db.scalar(
                    select(exists().where(ChaosExecution.batch_task_item_id == item_id))
                )

                if not has_executions:
                    # 没有执行记录，可以安全删除
                    await self.db.delete(existing_item)
                else:
                    # 有执行记录，不能删除，记录警告
                    logger.warning(f"子任务 {item_id} 有执行记录，无法删除")

    async def search_tasks_with_query(self, query: ChaosBatchTaskQuery) -> Tuple[List[ChaosBatchTask], int]:
        """使用查询参数搜索批次任务列表"""
        # 基础查询
        base_query = select(ChaosBatchTask).options(selectinload(ChaosBatchTask.task_items))
        
        # 筛选条件
        conditions = []
        
        # 关键词搜索（任务名称、描述）
        if query.keyword:
            conditions.append(
                or_(
                    ChaosBatchTask.name.ilike(f"%{query.keyword}%"),
                    ChaosBatchTask.description.ilike(f"%{query.keyword}%")
                )
            )
        
        # 环境ID筛选
        if query.env_id:
            conditions.append(ChaosBatchTask.env_id == query.env_id)
        
        # 批次执行模式筛选
        if query.batch_execution_mode:
            conditions.append(ChaosBatchTask.batch_execution_mode == query.batch_execution_mode)
        
        # 运行状态筛选
        if query.status:
            conditions.append(ChaosBatchTask.status == query.status)
        
        # 执行类型筛选
        if query.execution_type:
            conditions.append(ChaosBatchTask.execution_type == query.execution_type)
        
        # 任务状态筛选
        if query.task_status:
            conditions.append(ChaosBatchTask.task_status == query.task_status)
        
        # 创建者筛选
        if query.created_by:
            conditions.append(ChaosBatchTask.created_by == query.created_by)
        
        if conditions:
            base_query = base_query.where(and_(*conditions))
        
        # 计算总数
        total_query = select(func.count()).select_from(base_query.subquery())
        total_result = await self.db.execute(total_query)
        total = total_result.scalar()
        
        # 分页查询
        offset = (query.page - 1) * query.size
        base_query = base_query.offset(offset).limit(query.size).order_by(ChaosBatchTask.created_at.desc())
        result = await self.db.execute(base_query)
        items = result.scalars().all()
        
        return items, total

    async def get_by_env_id(self, env_id: int) -> List[ChaosBatchTask]:
        """根据环境ID获取批次任务列表"""
        query = (
            select(ChaosBatchTask)
            .options(selectinload(ChaosBatchTask.task_items))
            .where(ChaosBatchTask.env_id == env_id)
            .order_by(ChaosBatchTask.created_at.desc())
        )
        result = await self.db.execute(query)
        return result.scalars().all()

    async def get_by_status(self, status: str) -> List[ChaosBatchTask]:
        """根据状态获取批次任务列表"""
        query = (
            select(ChaosBatchTask)
            .options(selectinload(ChaosBatchTask.task_items))
            .where(ChaosBatchTask.status == status)
            .order_by(ChaosBatchTask.created_at.desc())
        )
        result = await self.db.execute(query)
        return result.scalars().all()

    async def get_executable_tasks(self) -> List[ChaosBatchTask]:
        """获取可执行的批次任务列表"""
        query = (
            select(ChaosBatchTask)
            .options(selectinload(ChaosBatchTask.task_items))
            .where(
                and_(
                    ChaosBatchTask.task_status == "enabled",
                    ChaosBatchTask.status.in_(["pending", "failed"])
                )
            )
            .order_by(ChaosBatchTask.created_at.desc())
        )
        result = await self.db.execute(query)
        return result.scalars().all()

    async def get_statistics(self) -> Dict[str, Any]:
        """获取批次任务统计信息"""
        # 总数统计
        total_query = select(func.count()).select_from(ChaosBatchTask)
        total_result = await self.db.execute(total_query)
        total_count = total_result.scalar()
        
        # 状态统计
        status_query = (
            select(ChaosBatchTask.status, func.count())
            .group_by(ChaosBatchTask.status)
        )
        status_result = await self.db.execute(status_query)
        status_stats = {row[0]: row[1] for row in status_result.fetchall()}
        
        # 执行模式统计
        mode_query = (
            select(ChaosBatchTask.batch_execution_mode, func.count())
            .group_by(ChaosBatchTask.batch_execution_mode)
        )
        mode_result = await self.db.execute(mode_query)
        execution_mode_stats = {row[0]: row[1] for row in mode_result.fetchall()}
        
        # 环境统计
        env_query = (
            select(ChaosBatchTask.env_id, func.count())
            .group_by(ChaosBatchTask.env_id)
        )
        env_result = await self.db.execute(env_query)
        env_stats = {str(row[0]): row[1] for row in env_result.fetchall()}
        
        return {
            "total_count": total_count,
            "status_stats": status_stats,
            "execution_mode_stats": execution_mode_stats,
            "env_stats": env_stats
        }

    async def delete_with_items(self, task_id: int) -> bool:
        """删除批次任务及其子任务"""
        task = await self.get(task_id)
        if not task:
            return False
        
        # 由于设置了cascade="all, delete-orphan"，删除主任务会自动删除子任务
        await self.db.delete(task)
        await self.db.commit()
        
        logger.info(f"批次任务删除成功: {task_id}")
        return True


class ChaosBatchTaskItemRepository(BaseRepository[ChaosBatchTaskItem, dict, dict]):
    """
    批次任务子项数据访问层
    """

    def __init__(self, db: AsyncSession):
        super().__init__(ChaosBatchTaskItem, db)

    async def get_by_batch_task_id(self, batch_task_id: int) -> List[ChaosBatchTaskItem]:
        """根据批次任务ID获取子任务列表"""
        query = (
            select(ChaosBatchTaskItem)
            .where(ChaosBatchTaskItem.batch_task_id == batch_task_id)
            .order_by(ChaosBatchTaskItem.task_order)
        )
        result = await self.db.execute(query)
        return result.scalars().all()

    async def get_by_order(self, batch_task_id: int, task_order: int) -> Optional[ChaosBatchTaskItem]:
        """根据批次任务ID和执行顺序获取子任务"""
        query = (
            select(ChaosBatchTaskItem)
            .where(
                and_(
                    ChaosBatchTaskItem.batch_task_id == batch_task_id,
                    ChaosBatchTaskItem.task_order == task_order
                )
            )
        )
        result = await self.db.execute(query)
        return result.scalar_one_or_none()

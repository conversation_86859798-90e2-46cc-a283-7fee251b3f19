from pydantic import BaseModel, EmailStr, Field, validator
from typing import Optional, List, Union
from datetime import datetime
from app.models.user import UserStatus

# ------------------------------
# 基础模型（公共字段）
# ------------------------------
class UserBase(BaseModel):
    """用户基础模型（包含公共字段）"""
    username: Optional[str] = Field(None, min_length=3, max_length=50, description="用户名")
    email: Optional[EmailStr] = Field(None, description="邮箱")
    phone: Optional[str] = Field(None, max_length=20, description="手机号")
    nickname: Optional[str] = Field(None, max_length=50, description="昵称")
    avatar: Optional[str] = Field(None, description="头像URL")
    status: Optional[UserStatus] = Field(None, description="用户状态")
    dept_id: Optional[int] = Field(None, description="部门ID")

# ------------------------------
# 请求模型（接收客户端数据）
# ------------------------------
class UserCreate(UserBase):
    """创建用户请求模型（必填字段）"""
    username: str = Field(..., min_length=3, max_length=50, description="用户名")
    email: EmailStr = Field(..., description="邮箱")
    password: str = Field(..., min_length=6, max_length=20, description="密码")  # 明文密码，服务层加密

class UserUpdate(UserBase):
    """更新用户请求模型（可选字段）"""
    password: Optional[str] = Field(None, min_length=6, max_length=20, description="新密码（可选）")

class UserQuery(BaseModel):
    """查询用户列表请求模型（分页+筛选）"""
    page: int = Field(1, ge=1, description="页码")
    size: int = Field(10, ge=1, le=100, description="每页条数")
    keyword: Optional[str] = Field(None, description="搜索关键词（匹配用户名/邮箱/昵称）")
    status: Optional[UserStatus] = Field(None, description="用户状态筛选")
    dept_id: Optional[int] = Field(None, description="部门ID筛选")
    
    @property
    def offset(self) -> int:
        """计算分页偏移量"""
        return (self.page - 1) * self.size

# ------------------------------
# 响应模型（返回给客户端的数据）
# ------------------------------
class UserResponse(UserBase):
    """用户详情响应模型"""
    id: int = Field(..., description="用户ID")
    is_superuser: bool = Field(..., description="是否超级管理员")
    last_login_time: Optional[datetime] = Field(None, description="最后登录时间")
    created_time: datetime = Field(..., description="创建时间")
    updated_time: datetime = Field(..., description="更新时间")
    role_ids: Optional[List[int]] = Field(None, description="关联的角色ID列表")  # 额外字段
    
    class Config:
        orm_mode = True  # 支持从ORM模型转换

class UserPageResponse(BaseModel):
    """用户列表分页响应模型"""
    items: List[UserResponse] = Field(..., description="当前页用户列表")
    total: int = Field(..., description="总条数")
    page: int = Field(..., description="当前页码")
    size: int = Field(..., description="每页条数")
    pages: int = Field(..., description="总页数")
    
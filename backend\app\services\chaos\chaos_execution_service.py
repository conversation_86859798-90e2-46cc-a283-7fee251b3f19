"""
混沌测试执行记录业务服务
"""
from typing import Dict, Any, Optional, Type
from datetime import datetime

from sqlalchemy.ext.asyncio import AsyncSession

from app.services.base import BaseService
from app.repositories.chaos.chaos_execution_repository import ChaosExecutionRepository
from app.repositories.chaos.chaos_task_repository import ChaosTaskRepository
from app.models.chaos.chaos_execution import ChaosExecution
from app.schemas.chaos.chaos_execution import (
    ChaosExecutionCreate, ChaosExecutionUpdate, ChaosExecutionResponse, ChaosExecutionListResponse, ChaosExecutionLogRequest, ChaosExecutionLogResponse,
    ChaosExecutionRetryRequest, ChaosExecutionBatchRequest,
    ChaosExecutionQuery, ChaosExecutionPageResponse
)
from app.schemas.base import PaginationResponse
from app.core.exceptions import raise_validation_error, raise_not_found
from app.utils.logger import setup_logger
from .chaosblade_service import ChaosBladeService

logger = setup_logger()


class ChaosExecutionService(BaseService[ChaosExecution, ChaosExecutionCreate, ChaosExecutionUpdate, ChaosExecutionResponse]):
    """
    混沌测试执行记录业务服务
    继承BaseService，提供执行记录管理的核心业务逻辑
    """

    def __init__(self, db: AsyncSession):
        self.repository = ChaosExecutionRepository(db)
        self.task_repository = ChaosTaskRepository(db)
        self.chaosblade_service = ChaosBladeService()
        super().__init__(db, self.repository)

    @property
    def model_class(self) -> Type[ChaosExecution]:
        return ChaosExecution

    @property
    def response_schema_class(self) -> Type[ChaosExecutionResponse]:
        return ChaosExecutionResponse

    # ==================== 钩子方法实现 ====================

    async def _validate_before_create(self, create_data: ChaosExecutionCreate, **kwargs) -> None:
        """创建前业务验证"""
        # 验证任务是否存在
        task = await self.task_repository.get(create_data.task_id)
        if not task:
            raise_validation_error(f"任务ID {create_data.task_id} 不存在")

    async def _process_before_create(self, create_dict: Dict[str, Any]) -> Dict[str, Any]:
        """创建前数据处理"""
        # 设置默认状态
        create_dict["status"] = "pending"
        create_dict["retry_count"] = 0
        create_dict["is_auto_destroyed"] = False
        
        return create_dict

    async def _process_after_create(self, obj: ChaosExecution, create_data) -> None:
        """创建后处理"""
        logger.info(f"执行记录创建成功: Task {obj.task_id}, Host {obj.host_id} (ID: {obj.id})")

    async def _convert_to_response(self, obj: ChaosExecution) -> ChaosExecutionResponse:
        """转换为响应对象"""
        # 调试信息
        logger.debug(f"转换执行记录 {obj.id}: start_time={obj.start_time}, end_time={obj.end_time}, "
                    f"duration_seconds={obj.duration_seconds}, destroy_time={obj.destroy_time}, "
                    f"created_by={obj.created_by}, updated_by={obj.updated_by}")

        # 准备数据字典，包含所有需要的字段
        data = {
            # 基础字段
            "id": obj.id,
            "task_id": obj.task_id,
            "host_id": obj.host_id,
            "start_time": obj.start_time,
            "end_time": obj.end_time,
            "status": obj.status,
            "chaos_uid": obj.chaos_uid,
            # 'host_info': obj.host_info,
            "command": obj.command,
            "blade_version": obj.blade_version,
            "output": obj.output,
            "error_message": obj.error_message,
            "exit_code": obj.exit_code,
            "fault_config": obj.fault_config,
            "duration_seconds": obj.duration_seconds,
            "retry_count": obj.retry_count,
            "is_auto_destroyed": obj.is_auto_destroyed,
            "destroy_time": obj.destroy_time,
            "destroy_output": obj.destroy_output,
            "created_at": obj.created_at,
            "updated_at": obj.updated_at,
            "created_by": await self.helper.get_user_display_name(obj.created_by) if obj.created_by else None,
            # 关联信息
            "task_name": None,
            "host_name": None,
            "environment_name": None,

            # 状态信息
            "is_running": obj.status == "running",
            "is_completed": obj.status in ["success", "failed", "cancelled"],
            "is_successful": obj.status == "success",
            "has_chaos_uid": bool(obj.chaos_uid)
        }

        # 设置关联信息
        try:
            # 任务信息
            if hasattr(obj, 'task') and obj.task:
                data["task_name"] = obj.task.name
                # 获取环境信息 - 从env_ids中获取第一个环境的名称
                if obj.task.env_ids and isinstance(obj.task.env_ids, list) and len(obj.task.env_ids) > 0:
                    env_id = obj.task.env_ids[0]
                    data["environment_name"] = await self.helper.get_environment_name(env_id)

            # 主机信息
            if obj.host_info and isinstance(obj.host_info, dict):
                data["host_name"] = obj.host_info.get('name') or obj.host_info.get('host')



        except Exception as e:
            logger.warning(f"获取关联信息失败: {str(e)}")
            pass

        return ChaosExecutionResponse(**data)

    async def get_by_id(self, obj_id: int) -> ChaosExecutionResponse:
        """重写基类方法以支持异步转换"""
        obj = await self.repository.get_with_task(obj_id)
        if not obj:
            from app.core.exceptions import raise_not_found
            raise_not_found(f"执行记录 ID {obj_id} 不存在")

        return await self._convert_to_response(obj)



    async def check_and_update_running_executions(self) -> Dict[str, Any]:
        """检查并更新正在运行的执行记录状态"""
        try:
            # 获取所有正在运行的执行记录
            running_executions = await self.repository.get_running_executions()

            updated_count = 0
            timeout_count = 0

            for execution in running_executions:
                # 检查是否超时（运行超过1小时视为超时）
                if execution.start_time:
                    from datetime import datetime, timedelta
                    timeout_threshold = datetime.now() - timedelta(hours=1)

                    if execution.start_time < timeout_threshold:
                        # 标记为超时失败
                        await self.repository.update_execution_status(
                            execution.id,
                            status="failed",
                            error_message="执行超时",
                            exit_code=-1
                        )
                        timeout_count += 1
                        updated_count += 1
                        logger.warning(f"执行记录 {execution.id} 超时，已标记为失败")

                # 这里可以添加更多的状态检查逻辑
                # 比如通过ChaosBlade命令检查实际状态

            return {
                "checked_count": len(running_executions),
                "updated_count": updated_count,
                "timeout_count": timeout_count
            }

        except Exception as e:
            logger.error(f"检查运行中执行记录失败: {str(e)}")
            return {
                "checked_count": 0,
                "updated_count": 0,
                "timeout_count": 0,
                "error": str(e)
            }

    async def _convert_to_list_response(self, obj: ChaosExecution) -> ChaosExecutionListResponse:
        """转换为列表响应对象"""
        # 获取关联信息
        task_name = None
        host_name = None
        try:
            if hasattr(obj, 'task') and obj.task:
                task_name = obj.task.name
            if obj.host_info and isinstance(obj.host_info, dict):
                host_name = obj.host_info.get('name') or obj.host_info.get('host')
        except Exception:
            pass

        return ChaosExecutionListResponse(
            id=obj.id,
            task_id=obj.task_id,
            task_name=task_name,
            host_id=obj.host_id,
            host_name=host_name,
            status=obj.status,
            chaos_uid=obj.chaos_uid,
            start_time=obj.start_time,
            end_time=obj.end_time,
            duration_seconds=obj.duration_seconds,
            retry_count=obj.retry_count,
            is_auto_destroyed=obj.is_auto_destroyed,
            exit_code=obj.exit_code,
            has_error=bool(obj.error_message),
            created_at=obj.created_at
        )

    # ==================== 业务方法 ====================

    async def list_executions(self, query: ChaosExecutionQuery) -> ChaosExecutionPageResponse:
        """查询执行记录列表，支持关键词搜索、状态筛选和分页"""
        executions, total = await self.repository.search_executions_with_query(query)

        # 转换为响应格式
        execution_responses = []
        for execution in executions:
            execution_response = await self._convert_to_list_response(execution)
            execution_responses.append(execution_response)

        # 计算总页数
        pages = (total + query.size - 1) // query.size

        return ChaosExecutionPageResponse(
            items=execution_responses,
            total=total,
            page=query.page,
            size=query.size,
            pages=pages
        )

    async def get_task_executions(self, task_id: int, page: int = 1, size: int = 20) -> PaginationResponse[ChaosExecutionListResponse]:
        """获取任务的执行记录"""
        skip = (page - 1) * size
        executions, total = await self.repository.get_task_executions(
            task_id, skip=skip, limit=size
        )

        # 使用统一的转换方法
        execution_list = []
        for execution in executions:
            execution_response = await self._convert_to_list_response(execution)
            execution_list.append(execution_response)

        return PaginationResponse(
            records=execution_list,
            total=total,
            current=page,
            size=size
        )

    async def get_execution_log(self, request: ChaosExecutionLogRequest) -> ChaosExecutionLogResponse:
        """获取执行日志"""
        execution = await self.repository.get(request.execution_id)
        if not execution:
            raise_not_found(f"执行记录ID {request.execution_id} 不存在")

        # 根据日志类型返回相应内容
        content = ""
        if request.log_type == "output":
            content = execution.output or ""
        elif request.log_type == "error":
            content = execution.error_message or ""
        elif request.log_type == "command":
            content = execution.command or ""
        elif request.log_type == "destroy":
            content = execution.destroy_output or ""

        return ChaosExecutionLogResponse(
            execution_id=request.execution_id,
            log_type=request.log_type,
            content=content,
            timestamp=datetime.now()
        )

    async def retry_execution(self, request: ChaosExecutionRetryRequest, current_user_id: int) -> ChaosExecutionResponse:
        """重试执行"""
        execution = await self.repository.get_with_task(request.execution_id)
        if not execution:
            raise_not_found(f"执行记录ID {request.execution_id} 不存在")

        if execution.is_running:
            raise_validation_error("正在运行的执行记录无法重试")

        try:
            # 增加重试次数
            execution.increment_retry()
            
            # 重置状态
            execution.status = "pending"
            execution.start_time = None
            execution.end_time = None
            execution.output = None
            execution.error_message = None
            execution.exit_code = None
            execution.chaos_uid = None
            execution.command = None
            
            await self.repository.db.commit()

            # 重新执行故障注入
            # 这里应该调用任务服务的执行方法
            logger.info(f"重试执行记录 {request.execution_id}，重试次数: {execution.retry_count}")

            return self._convert_to_response(execution)

        except Exception as e:
            logger.error(f"重试执行失败: {str(e)}")
            raise_validation_error(f"重试执行失败: {str(e)}")

    async def cancel_execution(self, execution_id: int, current_user_id: int) -> bool:
        """取消执行"""
        execution = await self.repository.get(execution_id)
        if not execution:
            raise_not_found(f"执行记录ID {execution_id} 不存在")

        if not execution.is_running:
            raise_validation_error("只能取消正在运行的执行记录")

        try:
            # 如果有chaos_uid，先销毁故障注入
            if execution.chaos_uid:
                # 这里需要获取主机信息并调用销毁命令
                host_info = await self._get_host_info(execution)
                await self.chaosblade_service.destroy_fault(host_info, execution.chaos_uid)

            # 更新状态为已取消
            execution.cancel_execution("用户取消")
            await self.repository.db.commit()

            logger.info(f"执行记录 {execution_id} 已取消，操作者: {current_user_id}")
            return True

        except Exception as e:
            logger.error(f"取消执行失败: {str(e)}")
            raise_validation_error(f"取消执行失败: {str(e)}")

    async def batch_operation(self, request: ChaosExecutionBatchRequest, current_user_id: int) -> Dict[str, Any]:
        """批量操作执行记录"""
        results = {
            "success_count": 0,
            "failed_count": 0,
            "errors": []
        }

        for execution_id in request.execution_ids:
            try:
                if request.action == "cancel":
                    await self.cancel_execution(execution_id, current_user_id)
                elif request.action == "destroy":
                    await self._destroy_execution(execution_id)
                elif request.action == "retry":
                    retry_request = ChaosExecutionRetryRequest(execution_id=execution_id)
                    await self.retry_execution(retry_request, current_user_id)
                elif request.action == "delete":
                    await self.delete(execution_id)
                
                results["success_count"] += 1

            except Exception as e:
                results["failed_count"] += 1
                results["errors"].append(f"执行记录 {execution_id}: {str(e)}")

        return results

    async def get_execution_statistics(self, task_id: Optional[int] = None) -> Dict[str, Any]:
        """获取执行统计信息"""
        try:
            # 使用repository的统计方法
            stats = await self.repository.get_statistics(task_id)

            # 计算成功率
            total_count = stats.get("total_count", 0)
            status_stats = stats.get("status_stats", {})
            success_count = status_stats.get('success', 0)
            success_rate = (success_count / total_count * 100) if total_count > 0 else 0

            # 获取最近的执行记录
            recent_executions = []
            try:
                if task_id:
                    result = await self.repository.get_task_executions(
                        task_id, skip=0, limit=5, order_by="created_at", desc=True
                    )
                else:
                    result = await self.repository.search_executions(
                        skip=0, limit=5, order_by="created_at", desc=True
                    )

                # 处理分页结果
                if hasattr(result, 'items'):
                    executions = result.items
                elif isinstance(result, tuple):
                    executions = result[0]
                else:
                    executions = []

                for execution in executions:
                    execution_response = await self._convert_to_list_response(execution)
                    recent_executions.append(execution_response)
            except Exception as e:
                logger.warning(f"获取最近执行记录失败: {str(e)}")
                recent_executions = []

            return {
                "total_count": total_count,
                "status_stats": status_stats,
                "success_rate": round(success_rate, 2),
                "avg_duration_seconds": stats.get("avg_duration_seconds", 0),
                "recent_executions": recent_executions
            }

        except Exception as e:
            logger.error(f"获取执行统计失败: {str(e)}")
            return {
                "total_count": 0,
                "status_stats": {},
                "success_rate": 0,
                "avg_duration_seconds": 0,
                "recent_executions": []
            }



    async def _destroy_execution(self, execution_id: int) -> None:
        """销毁执行记录的故障注入"""
        execution = await self.repository.get(execution_id)
        if not execution:
            return

        if execution.chaos_uid and not execution.is_auto_destroyed:
            try:
                # 获取主机信息
                host_info = await self._get_host_info_by_execution(execution)
                if not host_info:
                    logger.warning(f"执行记录 {execution_id} 缺少主机信息，无法销毁故障注入")
                    return

                # 调用ChaosBlade销毁命令
                result = await self.chaosblade_service.destroy_fault(host_info, execution.chaos_uid)

                if result.get("success", False):
                    # 销毁成功，更新记录
                    await self.repository.mark_as_destroyed(execution_id, result.get("output", "Destroyed successfully"))
                    logger.info(f"执行记录 {execution_id} 的故障注入已销毁")
                else:
                    # 销毁失败，记录错误
                    error_msg = result.get("error", "销毁失败")
                    logger.error(f"销毁执行记录 {execution_id} 的故障注入失败: {error_msg}")
                    # 即使销毁失败，也标记为已尝试销毁
                    await self.repository.mark_as_destroyed(execution_id, f"Destroy failed: {error_msg}")

            except Exception as e:
                logger.error(f"销毁执行记录 {execution_id} 的故障注入失败: {str(e)}")
                raise

    async def _get_host_info_by_execution(self, execution: 'ChaosExecution') -> Dict[str, Any]:
        """根据执行记录获取主机信息"""
        # 如果执行记录中保存了主机信息，直接使用
        if execution.host_info:
            return execution.host_info

        # 如果没有保存主机信息，从环境管理获取
        try:
            from app.services.env.env import EnvironmentService
            env_service = EnvironmentService(self.db)
            environment = await env_service.get_environment(execution.host_id)  # 使用host_id作为env_id

            if environment:
                return {
                    "host": environment.host,
                    "port": environment.port or 22,
                    "username": environment.config.get("username", "root") if environment.config else "root",
                    "password": environment.config.get("password") if environment.config else None,
                    "private_key_path": environment.config.get("private_key_path") if environment.config else None
                }
        except Exception as e:
            logger.error(f"获取执行记录 {execution.id} 的主机信息失败: {str(e)}")

        # 默认返回空字典
        return {}

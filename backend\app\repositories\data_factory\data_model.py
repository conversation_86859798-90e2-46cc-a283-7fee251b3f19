"""
数据模型仓库
提供数据模型的数据访问方法
"""
from typing import Optional, List, Dict, Any
from sqlalchemy import select, func, and_, or_
from sqlalchemy.ext.asyncio import AsyncSession

from app.repositories.base import BaseRepository
from app.models.data_factory.data_model import DataModel
from app.schemas.data_factory.data_model import DataModelCreate, DataModelUpdate


class DataModelRepository(BaseRepository[DataModel, DataModelCreate, DataModelUpdate]):
    """
    数据模型仓库类
    继承BaseRepository，提供数据模型特有的数据访问方法
    """

    def __init__(self, db: AsyncSession):
        super().__init__(DataModel, db)

    async def get_by_name(self, name: str) -> Optional[DataModel]:
        """
        根据模型名称获取数据模型
        
        Args:
            name: 模型名称
            
        Returns:
            数据模型实例或None
        """
        result = await self.db.execute(
            select(DataModel).where(DataModel.name == name)
        )
        return result.scalar_one_or_none()

    async def get_by_category(self, category: str, skip: int = 0, limit: int = 100) -> List[DataModel]:
        """
        根据分类获取数据模型列表
        
        Args:
            category: 模型分类
            skip: 跳过数量
            limit: 限制数量
            
        Returns:
            数据模型列表
        """
        result = await self.db.execute(
            select(DataModel)
            .where(DataModel.category == category)
            .offset(skip)
            .limit(limit)
            .order_by(DataModel.created_at.desc())
        )
        return result.scalars().all()

    async def search_by_keyword(self, keyword: str, skip: int = 0, limit: int = 100) -> List[DataModel]:
        """
        根据关键词搜索数据模型
        
        Args:
            keyword: 搜索关键词
            skip: 跳过数量
            limit: 限制数量
            
        Returns:
            数据模型列表
        """
        search_filter = or_(
            DataModel.name.ilike(f"%{keyword}%"),
            DataModel.description.ilike(f"%{keyword}%"),
            DataModel.category.ilike(f"%{keyword}%")
        )
        
        result = await self.db.execute(
            select(DataModel)
            .where(search_filter)
            .offset(skip)
            .limit(limit)
            .order_by(DataModel.created_at.desc())
        )
        return result.scalars().all()

    async def get_active_models(self, skip: int = 0, limit: int = 100) -> List[DataModel]:
        """
        获取启用状态的数据模型列表
        
        Args:
            skip: 跳过数量
            limit: 限制数量
            
        Returns:
            启用的数据模型列表
        """
        result = await self.db.execute(
            select(DataModel)
            .where(DataModel.status == "1")
            .offset(skip)
            .limit(limit)
            .order_by(DataModel.created_at.desc())
        )
        return result.scalars().all()

    async def increment_usage_count(self, model_id: int) -> None:
        """
        增加模型使用次数
        
        Args:
            model_id: 模型ID
        """
        model = await self.get(model_id)
        if model:
            model.usage_count = (model.usage_count or 0) + 1
            await self.db.commit()

    async def get_statistics(self) -> Dict[str, Any]:
        """
        获取数据模型统计信息
        
        Returns:
            统计信息字典
        """
        # 总数统计
        total_result = await self.db.execute(select(func.count(DataModel.id)))
        total_count = total_result.scalar()
        
        # 启用状态统计
        active_result = await self.db.execute(
            select(func.count(DataModel.id)).where(DataModel.status == "1")
        )
        active_count = active_result.scalar()
        
        # 分类统计
        category_result = await self.db.execute(
            select(DataModel.category, func.count(DataModel.id))
            .where(DataModel.category.isnot(None))
            .group_by(DataModel.category)
        )
        category_stats = dict(category_result.fetchall())
        
        # 使用次数统计
        usage_result = await self.db.execute(
            select(func.sum(DataModel.usage_count))
        )
        total_usage = usage_result.scalar() or 0
        
        return {
            'total_models': total_count,
            'active_models': active_count,
            'inactive_models': total_count - active_count,
            'category_distribution': category_stats,
            'total_usage': total_usage
        }

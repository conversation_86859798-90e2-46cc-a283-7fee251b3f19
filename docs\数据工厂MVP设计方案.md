# 数据工厂平台 - MVP设计方案

## 项目定位

### 核心理念
- **MVP优先**: 快速实现核心功能，验证价值
- **简单实用**: 避免过度设计，专注用户需求
- **预留扩展**: 架构设计考虑后期扩展性
- **渐进演进**: 基于用户反馈逐步完善功能

### 目标用户
- 中小型测试团队 (5-20人)
- 日常数据生成需求 (1万-10万条)
- 简单的数据模型和导出需求
- 基础的权限管理需求

## 1. 简化架构设计

### 1.1 MVP架构
```
┌─────────────────────────────────────────────────────────────┐
│                    前端界面层                                  │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │   模型管理       │ │   任务管理       │ │   数据预览       │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                    后端服务层                                  │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │   数据模型服务   │ │   生成引擎服务   │ │   任务管理服务   │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
│  ┌─────────────────┐ ┌─────────────────┐                     │
│  │   导出服务       │ │   用户权限服务   │                     │
│  └─────────────────┘ └─────────────────┘                     │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                    数据存储层                                  │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │      MySQL      │ │      Redis      │ │   本地文件       │ │
│  │   (主数据库)     │ │   (缓存+队列)   │ │   (临时存储)     │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 1.2 技术栈选择 (简化版)

**后端**:
- **框架**: FastAPI (与现有系统一致)
- **数据库**: MySQL (现有) + Redis (缓存)
- **任务队列**: 内置后台任务 (初期) → Celery (扩展期)
- **文件存储**: 本地文件系统 (初期) → MinIO (扩展期)
- **数据生成**: Python + Faker库

**前端**:
- **框架**: Vue 3 + TypeScript
- **UI组件**: Element Plus
- **状态管理**: Pinia
- **构建工具**: Vite

## 2. MVP核心功能

### 2.1 数据模型管理 (简化版)

#### 模型定义结构
```json
{
  "id": "user_model_v1",
  "name": "用户数据模型",
  "description": "简单的用户数据模型",
  "version": "1.0",
  "created_by": "admin",
  "created_at": "2024-01-01T00:00:00Z",
  "fields": [
    {
      "name": "user_id",
      "type": "string",
      "generator": "uuid",
      "description": "用户ID",
      "required": true
    },
    {
      "name": "username",
      "type": "string", 
      "generator": "name",
      "options": {
        "locale": "zh_CN"
      },
      "required": true
    },
    {
      "name": "age",
      "type": "integer",
      "generator": "range",
      "options": {
        "min": 18,
        "max": 65
      }
    }
  ]
}
```

#### 支持的数据类型 (MVP版本)
- **基础类型**: string, integer, decimal, boolean, date, datetime
- **生成器**: uuid, name, phone, email, address, range, sequence, random
- **扩展预留**: 后期可添加更多复杂类型

### 2.2 数据生成引擎 (简化版)

#### 生成策略
```python
class SimpleGenerationEngine:
    """简化版数据生成引擎"""
    
    def __init__(self):
        self.generators = self._load_builtin_generators()
        self.cache = {}
    
    async def generate_data(self, model_config: dict, count: int) -> List[dict]:
        """根据数据量选择生成策略"""
        if count <= 1000:
            return await self._instant_generate(model_config, count)
        elif count <= 50000:
            return await self._batch_generate(model_config, count)
        else:
            raise ValueError("数据量超出MVP版本限制，请联系管理员")
    
    async def _instant_generate(self, model_config: dict, count: int) -> List[dict]:
        """即时生成 (≤1000条)"""
        results = []
        for i in range(count):
            record = {}
            for field in model_config['fields']:
                generator = self.generators[field['generator']]
                record[field['name']] = await generator.generate(field.get('options', {}))
            results.append(record)
        return results
    
    async def _batch_generate(self, model_config: dict, count: int) -> str:
        """批量生成 (1000-50000条)"""
        # 分批生成，保存到临时文件
        batch_size = 5000
        temp_file = f"temp_data_{uuid.uuid4()}.json"
        
        with open(temp_file, 'w') as f:
            f.write('[')
            for batch_start in range(0, count, batch_size):
                batch_end = min(batch_start + batch_size, count)
                batch_data = await self._instant_generate(
                    model_config, batch_end - batch_start
                )
                
                if batch_start > 0:
                    f.write(',')
                json.dump(batch_data, f, ensure_ascii=False, indent=2)
            f.write(']')
        
        return temp_file
```

### 2.3 任务管理 (简化版)

#### 任务状态
```python
class TaskStatus(Enum):
    PENDING = "pending"      # 等待执行
    RUNNING = "running"      # 正在执行
    COMPLETED = "completed"  # 执行完成
    FAILED = "failed"        # 执行失败
    CANCELLED = "cancelled"  # 已取消

class GenerationTask(BaseModel):
    """简化版任务模型"""
    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False)
    model_id = Column(Integer, ForeignKey('data_models.id'))
    record_count = Column(Integer, nullable=False)
    status = Column(Enum(TaskStatus), default=TaskStatus.PENDING)
    progress = Column(Integer, default=0)  # 0-100
    result_file = Column(String(500))
    error_message = Column(Text)
    created_at = Column(DateTime, default=datetime.utcnow)
    completed_at = Column(DateTime)
    created_by = Column(String(50))
```

### 2.4 数据导出 (简化版)

#### 支持格式
- **JSON**: 标准JSON格式
- **CSV**: 逗号分隔值格式
- **Excel**: .xlsx格式 (使用openpyxl)

#### 导出实现
```python
class SimpleExporter:
    """简化版数据导出器"""
    
    async def export_json(self, data: List[dict], filename: str) -> str:
        """导出JSON格式"""
        filepath = f"exports/{filename}.json"
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        return filepath
    
    async def export_csv(self, data: List[dict], filename: str) -> str:
        """导出CSV格式"""
        if not data:
            return None
            
        filepath = f"exports/{filename}.csv"
        fieldnames = data[0].keys()
        
        with open(filepath, 'w', newline='', encoding='utf-8-sig') as f:
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(data)
        return filepath
    
    async def export_excel(self, data: List[dict], filename: str) -> str:
        """导出Excel格式"""
        filepath = f"exports/{filename}.xlsx"
        df = pd.DataFrame(data)
        df.to_excel(filepath, index=False, engine='openpyxl')
        return filepath
```

## 3. MVP界面设计

### 3.1 页面结构 (简化版)

#### 主要页面
1. **模型管理页面** (`/data-factory/models`)
   - 模型列表 (卡片式布局)
   - 新建/编辑模型 (简化表单)
   - 模型预览功能

2. **任务管理页面** (`/data-factory/tasks`)
   - 任务列表 (表格布局)
   - 新建任务对话框
   - 任务进度显示

3. **数据预览页面** (`/data-factory/preview`)
   - 基于模型的数据预览
   - 简单的数据统计

### 3.2 界面设计原则

#### 简化设计
- **单列布局**: 避免复杂的多栏设计
- **向导式流程**: 复杂操作分步骤完成
- **即时反馈**: 操作结果立即显示
- **最小化配置**: 提供合理默认值

#### 用户体验
- **快速上手**: 新用户5分钟内完成第一个数据生成
- **操作简单**: 常用功能不超过3步操作
- **错误友好**: 清晰的错误提示和解决建议

## 4. MVP实施计划

### 4.1 开发阶段 (8周)

#### 第一阶段: 基础功能 (Week 1-3)
- [x] 项目结构搭建
- [ ] 数据模型 CRUD
- [ ] 基础生成器实现 (8种)
- [ ] 简单任务管理
- [ ] JSON/CSV导出

#### 第二阶段: 界面开发 (Week 4-5)
- [ ] 模型管理界面
- [ ] 任务管理界面
- [ ] 数据预览界面
- [ ] 基础用户权限

#### 第三阶段: 完善优化 (Week 6-7)
- [ ] Excel导出功能
- [ ] 批量生成优化
- [ ] 错误处理完善
- [ ] 用户体验优化

#### 第四阶段: 测试部署 (Week 8)
- [ ] 功能测试
- [ ] 性能测试
- [ ] 部署配置
- [ ] 用户文档

### 4.2 扩展规划

#### 短期扩展 (3-6个月)
- [ ] 更多生成器类型
- [ ] 数据库直接插入
- [ ] 简单的字段关联
- [ ] 模板库功能

#### 中期扩展 (6-12个月)
- [ ] Celery异步任务
- [ ] 分布式生成
- [ ] 高级权限管理
- [ ] 监控面板

#### 长期扩展 (1年+)
- [ ] 微服务架构
- [ ] 自定义生成器
- [ ] API开放平台
- [ ] 多租户支持

## 5. 技术债务控制

### 5.1 架构预留

#### 扩展点设计
```python
# 生成器扩展点
class GeneratorRegistry:
    """生成器注册中心 - 支持动态扩展"""
    def register_generator(self, name: str, generator_class: Type[BaseGenerator])
    def get_generator(self, name: str) -> BaseGenerator

# 导出器扩展点  
class ExporterRegistry:
    """导出器注册中心 - 支持格式扩展"""
    def register_exporter(self, format: str, exporter_class: Type[BaseExporter])
    def get_exporter(self, format: str) -> BaseExporter

# 任务处理扩展点
class TaskProcessor:
    """任务处理器 - 支持处理策略扩展"""
    def register_processor(self, task_type: str, processor: TaskProcessorProtocol)
    def process_task(self, task: GenerationTask) -> TaskResult
```

### 5.2 代码质量

#### 质量标准 (MVP版本)
- **代码覆盖率**: >70% (降低标准，快速迭代)
- **代码规范**: 遵循基本PEP8规范
- **文档完整性**: 核心功能必须有文档
- **错误处理**: 关键路径必须有异常处理

#### 重构计划
- **第一次重构**: MVP完成后，优化核心生成引擎
- **第二次重构**: 用户反馈后，优化用户界面
- **第三次重构**: 扩展功能前，重构架构设计

## 6. 成功指标 (MVP版本)

### 6.1 功能指标
- [x] 支持5种以上数据类型
- [x] 支持3种导出格式
- [x] 支持1万条数据生成 (30秒内)
- [x] 基础的用户权限管理

### 6.2 性能指标
- [x] 小规模生成 (1000条) < 10秒
- [x] 中规模生成 (1万条) < 2分钟
- [x] 页面响应时间 < 3秒
- [x] 系统可用性 > 95%

### 6.3 用户指标
- [x] 新用户上手时间 < 10分钟
- [x] 核心功能使用率 > 80%
- [x] 用户满意度 > 4.0/5.0
- [x] 功能完成率 > 90%

---

**方案版本**: MVP v1.0  
**设计理念**: 简单实用，快速迭代，预留扩展  
**适用场景**: 中小型团队，基础数据生成需求  
**扩展策略**: 基于用户反馈，渐进式功能增强

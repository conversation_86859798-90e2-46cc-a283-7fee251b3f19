# 测试数据工厂平台 - 优化版设计方案

## 项目概述

### 目标定位
为中小型测试平台提供企业级的测试数据生成解决方案，在保证功能完整性的同时，确保系统的高性能、高可用性和可扩展性。

### 核心价值
- **高性能**: 支持大规模数据生成，优化内存和计算资源使用
- **高可用**: 分布式架构，支持任务容错和自动恢复
- **易扩展**: 模块化设计，支持自定义生成器和插件扩展
- **企业级**: 完善的权限管理、审计日志和安全机制

## 1. 优化架构设计

### 1.1 整体架构

```
┌─────────────────────────────────────────────────────────────┐
│                    前端界面层                                  │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │   模型设计器     │ │   任务管理面板   │ │   数据预览器     │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │   权限管理       │ │   监控面板       │ │   模板库         │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                    API网关层                                   │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │   认证授权       │ │   请求路由       │ │   限流熔断       │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                    业务服务层                                  │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │   数据模型服务   │ │   生成引擎服务   │ │   任务管理服务   │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │   权限管理服务   │ │   导出处理服务   │ │   连接器服务     │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │   缓存管理服务   │ │   监控日志服务   │ │   文件管理服务   │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                    消息队列层                                  │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │   任务队列       │ │   结果通知       │ │   系统事件       │ │
│  │   (Celery)      │ │   (WebSocket)   │ │   (EventBus)    │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                    数据存储层                                  │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │      MySQL      │ │      Redis      │ │   MinIO/S3      │ │
│  │   (主数据库)     │ │  (缓存+队列)    │ │   (文件存储)     │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │   Elasticsearch │ │   InfluxDB      │ │   备份存储       │ │
│  │   (日志搜索)     │ │   (监控指标)    │ │   (数据备份)     │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 1.2 技术栈选择

**后端核心**:
- **框架**: FastAPI 0.104.1 + Uvicorn
- **数据库**: MySQL 8.0 + Redis 7.0
- **任务队列**: Celery + Redis
- **文件存储**: MinIO (兼容S3)
- **监控**: Prometheus + Grafana
- **日志**: ELK Stack (Elasticsearch + Logstash + Kibana)

**前端**:
- **框架**: Vue 3.4 + TypeScript 5.0
- **UI组件**: Element Plus 2.4
- **状态管理**: Pinia 2.1
- **构建工具**: Vite 5.0

**基础设施**:
- **容器化**: Docker + Docker Compose
- **反向代理**: Nginx
- **消息通知**: WebSocket + Server-Sent Events

## 2. 核心功能优化设计

### 2.1 数据模型管理增强

#### 模型定义结构优化
```json
{
  "id": "user_order_model_v2",
  "name": "用户订单模型",
  "description": "企业级用户订单数据模型",
  "version": "2.1.0",
  "category": "电商业务",
  "tags": ["用户", "订单", "电商"],
  "metadata": {
    "created_by": "admin",
    "created_at": "2024-01-01T00:00:00Z",
    "updated_by": "admin", 
    "updated_at": "2024-01-15T10:30:00Z",
    "usage_count": 156,
    "last_used": "2024-01-20T14:20:00Z"
  },
  "permissions": {
    "owner": "admin",
    "editors": ["user1", "user2"],
    "viewers": ["team_qa"],
    "public": false
  },
  "validation_rules": {
    "required_fields": ["user_id", "order_id"],
    "unique_fields": ["order_id"],
    "business_rules": [
      {
        "rule": "order_amount > 0",
        "message": "订单金额必须大于0"
      }
    ]
  },
  "fields": [
    {
      "name": "user_id",
      "type": "string",
      "generator": "uuid",
      "description": "用户唯一标识",
      "constraints": {
        "required": true,
        "unique": false,
        "format": "uuid"
      },
      "metadata": {
        "sensitive": false,
        "indexable": true
      }
    }
  ]
}
```

#### 支持的数据类型扩展
- **基础类型**: string, integer, decimal, boolean, date, datetime, timestamp
- **复合类型**: json, array, nested_object
- **生成器类型**: uuid, name, phone, email, address, url, text, image_url
- **数值类型**: range, sequence, random, normal_distribution
- **时间类型**: date_range, datetime_range, relative_time
- **关联类型**: foreign_key, reference, calculated_field
- **地理类型**: coordinate, address_component, region_code
- **业务类型**: id_card, bank_card, license_plate, social_credit

### 2.2 高性能数据生成引擎

#### 分布式生成架构
```python
class DistributedGenerationEngine:
    """分布式数据生成引擎"""
    
    def __init__(self):
        self.task_scheduler = CeleryTaskScheduler()
        self.memory_manager = MemoryManager()
        self.cache_manager = CacheManager()
        self.file_manager = FileManager()
    
    async def generate_data(self, model_config: dict, count: int, options: dict):
        """智能数据生成调度"""
        if count <= 1000:
            return await self._instant_generate(model_config, count)
        elif count <= 100000:
            return await self._batch_generate(model_config, count, options)
        else:
            return await self._distributed_generate(model_config, count, options)
    
    async def _distributed_generate(self, model_config: dict, count: int, options: dict):
        """分布式大规模生成"""
        # 任务分片
        chunk_size = 50000
        chunks = [(i, min(i + chunk_size, count)) for i in range(0, count, chunk_size)]
        
        # 并行执行
        tasks = []
        for start, end in chunks:
            task = self.task_scheduler.delay(
                'generate_data_chunk',
                model_config=model_config,
                start=start,
                end=end,
                options=options
            )
            tasks.append(task)
        
        # 结果合并
        return await self._merge_results(tasks, options)
```

#### 内存优化策略
```python
class MemoryManager:
    """内存管理器"""
    
    def __init__(self):
        self.max_memory_per_task = 2 * 1024 * 1024 * 1024  # 2GB
        self.batch_sizes = {
            'small': 1000,
            'medium': 10000, 
            'large': 50000
        }
    
    def calculate_optimal_batch_size(self, model_config: dict, available_memory: int):
        """计算最优批次大小"""
        field_count = len(model_config['fields'])
        estimated_record_size = self._estimate_record_size(model_config)
        
        optimal_batch = min(
            available_memory // (estimated_record_size * 2),  # 预留50%内存
            self.batch_sizes['large']
        )
        
        return max(optimal_batch, self.batch_sizes['small'])
```

### 2.3 企业级任务管理系统

#### 任务状态机优化
```python
class TaskStateMachine:
    """任务状态机"""
    
    STATES = {
        'PENDING': '等待执行',
        'QUEUED': '已入队列',
        'RUNNING': '正在执行',
        'PAUSED': '已暂停',
        'COMPLETED': '执行完成',
        'FAILED': '执行失败',
        'CANCELLED': '已取消',
        'TIMEOUT': '执行超时',
        'RETRY': '重试中'
    }
    
    TRANSITIONS = {
        'PENDING': ['QUEUED', 'CANCELLED'],
        'QUEUED': ['RUNNING', 'CANCELLED'],
        'RUNNING': ['COMPLETED', 'FAILED', 'PAUSED', 'TIMEOUT'],
        'PAUSED': ['RUNNING', 'CANCELLED'],
        'FAILED': ['RETRY', 'CANCELLED'],
        'RETRY': ['RUNNING', 'FAILED', 'CANCELLED'],
        'TIMEOUT': ['RETRY', 'CANCELLED']
    }
```

#### 任务优先级和资源管理
```python
class TaskPriorityManager:
    """任务优先级管理"""
    
    PRIORITY_LEVELS = {
        'URGENT': 1,     # 紧急任务
        'HIGH': 2,       # 高优先级
        'NORMAL': 3,     # 普通优先级
        'LOW': 4,        # 低优先级
        'BATCH': 5       # 批处理任务
    }
    
    RESOURCE_LIMITS = {
        'max_concurrent_tasks': 10,
        'max_memory_per_user': 8 * 1024 * 1024 * 1024,  # 8GB
        'max_cpu_cores_per_task': 4,
        'max_execution_time': 3600  # 1小时
    }
```

## 3. 安全和权限管理

### 3.1 RBAC权限模型
```python
class Permission(Enum):
    # 数据模型权限
    CREATE_MODEL = "create_model"
    EDIT_MODEL = "edit_model"
    DELETE_MODEL = "delete_model"
    VIEW_MODEL = "view_model"
    COPY_MODEL = "copy_model"
    
    # 数据生成权限
    GENERATE_DATA = "generate_data"
    GENERATE_LARGE_DATA = "generate_large_data"  # >10万条
    EXPORT_DATA = "export_data"
    
    # 任务管理权限
    VIEW_TASKS = "view_tasks"
    MANAGE_TASKS = "manage_tasks"
    CANCEL_TASKS = "cancel_tasks"
    
    # 系统管理权限
    MANAGE_USERS = "manage_users"
    MANAGE_PERMISSIONS = "manage_permissions"
    VIEW_SYSTEM_METRICS = "view_system_metrics"

class Role(Enum):
    SUPER_ADMIN = "super_admin"      # 超级管理员
    ADMIN = "admin"                  # 管理员
    DEVELOPER = "developer"          # 开发者
    TESTER = "tester"               # 测试人员
    VIEWER = "viewer"               # 只读用户
```

### 3.2 数据安全机制
```python
class DataSecurityManager:
    """数据安全管理器"""
    
    def __init__(self):
        self.encryption_key = self._load_encryption_key()
        self.audit_logger = AuditLogger()
    
    def mask_sensitive_data(self, data: dict, model_config: dict) -> dict:
        """敏感数据脱敏"""
        sensitive_fields = self._get_sensitive_fields(model_config)
        
        for field in sensitive_fields:
            if field in data:
                data[field] = self._mask_field_value(data[field], field)
        
        return data
    
    def encrypt_export_file(self, file_path: str, password: str) -> str:
        """导出文件加密"""
        encrypted_path = f"{file_path}.encrypted"
        # 实现文件加密逻辑
        return encrypted_path
    
    def log_data_access(self, user_id: str, action: str, resource: str, details: dict):
        """记录数据访问日志"""
        self.audit_logger.log({
            'user_id': user_id,
            'action': action,
            'resource': resource,
            'details': details,
            'timestamp': datetime.utcnow(),
            'ip_address': self._get_client_ip()
        })
```

## 4. 性能优化策略

### 4.1 缓存策略
```python
class CacheStrategy:
    """缓存策略配置"""
    
    CACHE_CONFIGS = {
        # 模型配置缓存
        'model_config': {
            'ttl': 3600,  # 1小时
            'max_size': 1000,
            'eviction_policy': 'LRU'
        },
        
        # 生成器结果缓存
        'generator_results': {
            'ttl': 1800,  # 30分钟
            'max_size': 5000,
            'eviction_policy': 'LFU'
        },
        
        # 用户权限缓存
        'user_permissions': {
            'ttl': 7200,  # 2小时
            'max_size': 10000,
            'eviction_policy': 'TTL'
        },
        
        # 模板库缓存
        'template_library': {
            'ttl': 21600,  # 6小时
            'max_size': 500,
            'eviction_policy': 'LRU'
        }
    }
```

### 4.2 数据库优化
```python
class DatabaseOptimization:
    """数据库优化配置"""
    
    CONNECTION_POOL = {
        'pool_size': 20,
        'max_overflow': 30,
        'pool_timeout': 30,
        'pool_recycle': 3600,
        'pool_pre_ping': True
    }
    
    BATCH_OPERATIONS = {
        'insert_batch_size': 5000,
        'update_batch_size': 2000,
        'bulk_insert_threshold': 10000
    }
    
    INDEX_STRATEGY = {
        'data_models': ['name', 'created_by', 'created_at'],
        'generation_tasks': ['status', 'created_by', 'created_at'],
        'audit_logs': ['user_id', 'action', 'timestamp'],
        'user_permissions': ['user_id', 'resource_type']
    }
```

## 5. 监控和运维

### 5.1 系统监控指标
```python
class MonitoringMetrics:
    """监控指标定义"""
    
    SYSTEM_METRICS = [
        'cpu_usage_percent',
        'memory_usage_percent', 
        'disk_usage_percent',
        'network_io_bytes',
        'database_connections',
        'redis_memory_usage'
    ]
    
    BUSINESS_METRICS = [
        'task_success_rate',
        'data_generation_speed',
        'user_active_count',
        'model_usage_frequency',
        'export_success_rate',
        'api_response_time'
    ]
    
    ERROR_METRICS = [
        'error_rate_percent',
        'timeout_rate_percent',
        'system_availability_percent',
        'database_error_count',
        'task_failure_count'
    ]
```

### 5.2 告警策略
```python
class AlertStrategy:
    """告警策略配置"""
    
    ALERT_RULES = {
        'high_cpu_usage': {
            'condition': 'cpu_usage_percent > 80',
            'duration': '5m',
            'severity': 'warning'
        },
        
        'high_memory_usage': {
            'condition': 'memory_usage_percent > 85',
            'duration': '3m', 
            'severity': 'critical'
        },
        
        'task_failure_rate': {
            'condition': 'task_failure_rate > 10',
            'duration': '10m',
            'severity': 'warning'
        },
        
        'database_connection_exhaustion': {
            'condition': 'database_connections > 90',
            'duration': '2m',
            'severity': 'critical'
        }
    }
```

## 6. UI/UX 优化设计

### 6.1 桌面端优化界面设计

#### 模型设计器优化
采用**两步式向导设计**，降低复杂度：

**Step 1: 模型基础信息**
- 简化的单列表单布局
- 模型名称、描述、分类、标签
- 权限设置（所有者、编辑者、查看者）
- 业务规则配置

**Step 2: 字段设计与预览**
- 左侧：字段库（可折叠分类）
- 中间：字段配置区域（拖拽或点击添加）
- 右侧：实时预览面板（可折叠）

**交互优化**：
- 智能字段推荐：基于模型名称和描述推荐相关字段
- 批量字段操作：支持批量添加、删除、排序
- 配置模板：提供常用字段配置模板
- 实时验证：字段配置实时验证和错误提示

#### 任务管理面板优化
```
┌─────────────────────────────────────────────────────────────┐
│  任务概览仪表板                                                │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │
│  │ 运行中: 5   │ │ 队列中: 12  │ │ 今日完成: 28│ │ 失败率: 2%  │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│  任务列表 (支持多维度筛选和排序)                                │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ 筛选器: [状态] [模型] [创建时间] [数据量] [创建者]        │ │
│  └─────────────────────────────────────────────────────────┘ │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ 任务表格 (虚拟滚动，支持大量数据)                        │ │
│  │ - 实时状态更新 (WebSocket)                              │ │
│  │ - 进度条动画                                            │ │
│  │ - 批量操作工具栏                                        │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

#### 数据预览器增强
- **多视图模式**：表格视图、JSON视图、图表视图
- **数据质量检查**：重复率、空值率、格式正确率统计
- **样本数据导出**：支持导出预览数据进行测试
- **字段统计分析**：数值分布、字符串长度分布等

### 6.2 主题和视觉优化

#### 深色主题优化
```css
/* 深色主题色彩方案 */
:root[theme="dark"] {
  --bg-primary: #1a1a1a;
  --bg-secondary: #2d2d2d;
  --bg-tertiary: #3a3a3a;

  --text-primary: #ffffff;
  --text-secondary: #b3b3b3;
  --text-tertiary: #808080;

  --border-primary: #404040;
  --border-secondary: #333333;

  --accent-primary: #409eff;
  --accent-success: #67c23a;
  --accent-warning: #e6a23c;
  --accent-danger: #f56c6c;

  --shadow-light: 0 2px 12px rgba(0, 0, 0, 0.3);
  --shadow-heavy: 0 4px 20px rgba(0, 0, 0, 0.5);
}
```

#### 数据可视化优化
- **进度指示器**：环形进度条、线性进度条、步骤进度条
- **状态指示器**：彩色标签、图标状态、动画效果
- **数据图表**：生成速度趋势、任务状态分布、用户活跃度
- **交互反馈**：加载动画、操作确认、成功提示

## 7. API 设计优化

### 7.1 RESTful API 规范
```python
# 数据模型管理 API
GET    /api/v1/data-models                    # 获取模型列表
POST   /api/v1/data-models                    # 创建数据模型
GET    /api/v1/data-models/{id}               # 获取模型详情
PUT    /api/v1/data-models/{id}               # 更新数据模型
DELETE /api/v1/data-models/{id}               # 删除数据模型
POST   /api/v1/data-models/{id}/copy          # 复制数据模型
POST   /api/v1/data-models/{id}/preview       # 预览数据
GET    /api/v1/data-models/{id}/versions      # 获取版本历史

# 数据生成 API
POST   /api/v1/generation-tasks               # 创建生成任务
GET    /api/v1/generation-tasks               # 获取任务列表
GET    /api/v1/generation-tasks/{id}          # 获取任务详情
PUT    /api/v1/generation-tasks/{id}/pause    # 暂停任务
PUT    /api/v1/generation-tasks/{id}/resume   # 恢复任务
PUT    /api/v1/generation-tasks/{id}/cancel   # 取消任务
DELETE /api/v1/generation-tasks/{id}          # 删除任务
GET    /api/v1/generation-tasks/{id}/download # 下载结果

# 权限管理 API
GET    /api/v1/permissions/users/{id}         # 获取用户权限
PUT    /api/v1/permissions/users/{id}         # 更新用户权限
GET    /api/v1/permissions/models/{id}        # 获取模型权限
PUT    /api/v1/permissions/models/{id}        # 更新模型权限

# 系统监控 API
GET    /api/v1/metrics/system                 # 系统指标
GET    /api/v1/metrics/business               # 业务指标
GET    /api/v1/health                         # 健康检查
```

### 7.2 WebSocket 实时通信
```python
class WebSocketManager:
    """WebSocket 连接管理"""

    def __init__(self):
        self.connections = {}
        self.user_subscriptions = {}

    async def subscribe_task_updates(self, user_id: str, task_ids: List[str]):
        """订阅任务状态更新"""
        if user_id not in self.user_subscriptions:
            self.user_subscriptions[user_id] = set()

        self.user_subscriptions[user_id].update(task_ids)

    async def broadcast_task_update(self, task_id: str, update_data: dict):
        """广播任务状态更新"""
        for user_id, subscriptions in self.user_subscriptions.items():
            if task_id in subscriptions:
                await self._send_to_user(user_id, {
                    'type': 'task_update',
                    'task_id': task_id,
                    'data': update_data
                })

    async def send_system_notification(self, message: str, level: str = 'info'):
        """发送系统通知"""
        notification = {
            'type': 'system_notification',
            'message': message,
            'level': level,
            'timestamp': datetime.utcnow().isoformat()
        }

        for connection in self.connections.values():
            await connection.send_json(notification)
```

## 8. 部署和运维优化

### 8.1 Docker 容器化部署
```yaml
# docker-compose.yml
version: '3.8'

services:
  # API 服务
  api:
    build: ./backend
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=mysql+aiomysql://user:pass@mysql:3306/dptest
      - REDIS_URL=redis://redis:6379/0
      - MINIO_ENDPOINT=minio:9000
    depends_on:
      - mysql
      - redis
      - minio
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped

  # Celery Worker
  worker:
    build: ./backend
    command: celery -A app.core.celery worker --loglevel=info --concurrency=4
    environment:
      - DATABASE_URL=mysql+aiomysql://user:pass@mysql:3306/dptest
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - mysql
      - redis
    volumes:
      - ./data:/app/data
    restart: unless-stopped

  # Celery Beat (定时任务)
  beat:
    build: ./backend
    command: celery -A app.core.celery beat --loglevel=info
    environment:
      - DATABASE_URL=mysql+aiomysql://user:pass@mysql:3306/dptest
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - mysql
      - redis
    restart: unless-stopped

  # 前端服务
  frontend:
    build: ./frontend
    ports:
      - "3000:80"
    depends_on:
      - api
    restart: unless-stopped

  # MySQL 数据库
  mysql:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=rootpassword
      - MYSQL_DATABASE=dptest
      - MYSQL_USER=dptest
      - MYSQL_PASSWORD=password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./scripts/init.sql:/docker-entrypoint-initdb.d/init.sql
    restart: unless-stopped

  # Redis 缓存
  redis:
    image: redis:7.0-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

  # MinIO 对象存储
  minio:
    image: minio/minio:latest
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      - MINIO_ROOT_USER=minioadmin
      - MINIO_ROOT_PASSWORD=minioadmin
    command: server /data --console-address ":9001"
    volumes:
      - minio_data:/data
    restart: unless-stopped

  # Nginx 反向代理
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - api
      - frontend
    restart: unless-stopped

volumes:
  mysql_data:
  redis_data:
  minio_data:
```

### 8.2 监控和日志配置
```yaml
# monitoring/docker-compose.monitoring.yml
version: '3.8'

services:
  # Prometheus 监控
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
    restart: unless-stopped

  # Grafana 可视化
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./grafana/datasources:/etc/grafana/provisioning/datasources
    restart: unless-stopped

  # Elasticsearch 日志存储
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
      - xpack.security.enabled=false
    ports:
      - "9200:9200"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    restart: unless-stopped

  # Kibana 日志分析
  kibana:
    image: docker.elastic.co/kibana/kibana:8.11.0
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    depends_on:
      - elasticsearch
    restart: unless-stopped

volumes:
  prometheus_data:
  grafana_data:
  elasticsearch_data:
```

## 9. 数据备份和恢复策略

### 9.1 自动备份配置
```python
class BackupManager:
    """备份管理器"""

    def __init__(self):
        self.backup_strategies = {
            'mysql': MySQLBackupStrategy(),
            'redis': RedisBackupStrategy(),
            'minio': MinIOBackupStrategy(),
            'elasticsearch': ElasticsearchBackupStrategy()
        }

    async def create_full_backup(self):
        """创建完整备份"""
        backup_id = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        tasks = []
        for service, strategy in self.backup_strategies.items():
            task = asyncio.create_task(
                strategy.backup(backup_id)
            )
            tasks.append(task)

        results = await asyncio.gather(*tasks, return_exceptions=True)

        return {
            'backup_id': backup_id,
            'timestamp': datetime.utcnow(),
            'results': dict(zip(self.backup_strategies.keys(), results))
        }

    async def restore_from_backup(self, backup_id: str):
        """从备份恢复"""
        for service, strategy in self.backup_strategies.items():
            await strategy.restore(backup_id)
```

### 9.2 灾难恢复计划
```python
class DisasterRecoveryPlan:
    """灾难恢复计划"""

    RECOVERY_PROCEDURES = {
        'database_corruption': [
            '1. 停止所有写入操作',
            '2. 从最近的备份恢复数据库',
            '3. 重放事务日志',
            '4. 验证数据完整性',
            '5. 恢复服务'
        ],

        'storage_failure': [
            '1. 切换到备用存储',
            '2. 从备份恢复文件',
            '3. 更新存储配置',
            '4. 验证文件完整性',
            '5. 恢复服务'
        ],

        'service_unavailable': [
            '1. 检查服务状态',
            '2. 重启相关服务',
            '3. 检查依赖服务',
            '4. 验证服务功能',
            '5. 监控服务稳定性'
        ]
    }

    RTO = 30  # 恢复时间目标：30分钟
    RPO = 15  # 恢复点目标：15分钟数据丢失
```

## 10. 优化后的MVP实施路线图

### 10.1 MVP功能优先级重新定义

#### 第一优先级 (Must Have) - 4周
- [x] **用户认证和权限管理**: JWT认证 + RBAC权限控制
- [x] **数据模型CRUD**: 创建、编辑、删除、查看数据模型
- [x] **基础数据生成**: 支持10种常用生成器，≤1000条即时生成
- [x] **JSON/CSV导出**: 基础格式导出功能
- [x] **简单任务管理**: 任务状态跟踪和历史记录

#### 第二优先级 (Should Have) - 6周
- [x] **消息队列集成**: Celery + Redis 异步任务处理
- [x] **批处理任务**: 大规模数据后台生成 (>1000条)
- [x] **Excel/SQL导出**: 扩展导出格式支持
- [x] **数据库连接器**: MySQL/PostgreSQL直接插入
- [x] **实时进度显示**: WebSocket实时任务状态更新
- [x] **缓存优化**: Redis缓存策略实现

#### 第三优先级 (Could Have) - 4周
- [x] **模板库系统**: 常用数据模型模板
- [x] **字段关联功能**: 简单的字段依赖关系
- [x] **数据预览增强**: 多视图模式和质量检查
- [x] **监控面板**: 系统性能和业务指标监控
- [x] **审计日志**: 用户操作记录和数据访问日志

#### 第四优先级 (Nice to Have) - 4周
- [x] **自定义生成器**: 用户自定义数据生成逻辑
- [x] **API接口开放**: 外部系统调用接口
- [x] **数据统计分析**: 生成数据的质量分析
- [x] **高级权限控制**: 细粒度权限和数据脱敏
- [x] **性能优化**: 分布式生成和高级缓存策略

### 10.2 技术债务管理

#### 代码质量保证
```python
# 技术债务监控指标
TECHNICAL_DEBT_METRICS = {
    'code_coverage': {
        'target': 80,
        'current': 0,
        'trend': 'increasing'
    },
    'code_duplication': {
        'target': 5,  # 5%以下
        'current': 0,
        'trend': 'decreasing'
    },
    'cyclomatic_complexity': {
        'target': 10,  # 平均复杂度<10
        'current': 0,
        'trend': 'stable'
    },
    'technical_debt_ratio': {
        'target': 5,  # 5%以下
        'current': 0,
        'trend': 'decreasing'
    }
}
```

#### 重构计划
```python
REFACTORING_PLAN = {
    'phase_1': {
        'duration': '2周',
        'focus': '核心生成引擎重构',
        'goals': ['提高代码复用性', '优化性能', '增强可测试性']
    },
    'phase_2': {
        'duration': '1周',
        'focus': 'API接口标准化',
        'goals': ['统一响应格式', '完善错误处理', '增强文档']
    },
    'phase_3': {
        'duration': '1周',
        'focus': '前端组件优化',
        'goals': ['组件复用', '性能优化', '用户体验提升']
    }
}
```

## 11. 成功指标和KPI

### 11.1 技术指标
```python
TECHNICAL_KPI = {
    # 性能指标
    'api_response_time_p95': '<200ms',
    'page_load_time': '<3s',
    'data_generation_speed': '>1000条/秒',
    'concurrent_users': '>100',

    # 可用性指标
    'system_uptime': '>99.5%',
    'error_rate': '<0.1%',
    'mttr': '<5分钟',  # 平均恢复时间
    'mtbf': '>720小时',  # 平均故障间隔

    # 质量指标
    'code_coverage': '>80%',
    'bug_density': '<5个/KLOC',
    'security_vulnerabilities': '0个高危',
    'performance_regression': '0个'
}
```

### 11.2 业务指标
```python
BUSINESS_KPI = {
    # 用户指标
    'user_satisfaction': '>4.5/5.0',
    'user_adoption_rate': '>80%',
    'user_retention_rate': '>90%',
    'new_user_onboarding_time': '<30分钟',

    # 功能指标
    'feature_usage_rate': '>70%',
    'task_success_rate': '>98%',
    'data_export_success_rate': '>99%',
    'model_creation_success_rate': '>95%',

    # 效率指标
    'data_generation_efficiency': '提升300%',
    'test_data_preparation_time': '减少80%',
    'manual_work_reduction': '>90%',
    'cost_saving': '>50%'
}
```

### 11.3 运营指标
```python
OPERATIONAL_KPI = {
    # 系统使用指标
    'daily_active_users': '>50',
    'monthly_active_users': '>200',
    'peak_concurrent_tasks': '>20',
    'total_data_generated': '>1000万条/月',

    # 支持指标
    'support_ticket_resolution_time': '<4小时',
    'user_feedback_response_time': '<24小时',
    'documentation_completeness': '>95%',
    'training_completion_rate': '>90%'
}
```

---

**文档版本**: v2.0.0
**最后更新**: 2024-01-30
**适用项目**: DpTestPlatform DataFactory
**重要更新**: 完整的企业级优化方案，包含分布式架构、安全机制、监控体系和部署方案

from sqlalchemy.future import select
from sqlalchemy.orm import Session
from sqlalchemy import func
from typing import Optional, List, Dict, Any
from app.models.user import User, UserStatus
from app.schemas.user_schema import UserCreate, UserUpdate

class UserCRUD:
    """用户数据访问层"""
    
    def __init__(self, db: Session):
        self.db = db  # 数据库会话
    
    async def get_by_id(self, user_id: int) -> Optional[User]:
        """根据ID查询用户"""
        result = await self.db.execute(select(User).filter(User.id == user_id))
        return result.scalars().first()
    
    async def get_by_username(self, username: str) -> Optional[User]:
        """根据用户名查询用户（用于登录验证）"""
        result = await self.db.execute(select(User).filter(User.username == username))
        return result.scalars().first()
    
    async def get_by_email(self, email: str) -> Optional[User]:
        """根据邮箱查询用户（用于邮箱唯一性校验）"""
        result = await self.db.execute(select(User).filter(User.email == email))
        return result.scalars().first()
    
    async def list(
        self, 
        keyword: Optional[str] = None,
        status: Optional[UserStatus] = None,
        dept_id: Optional[int] = None,
        offset: int = 0,
        limit: int = 10
    ) -> (List[User], int):
        """查询用户列表（带筛选和分页）"""
        # 基础查询
        query = select(User)
        
        # 筛选条件
        if keyword:
            query = query.filter(
                (User.username.ilike(f"%{keyword}%")) |
                (User.email.ilike(f"%{keyword}%")) |
                (User.nickname.ilike(f"%{keyword}%"))
            )
        if status:
            query = query.filter(User.status == status)
        if dept_id:
            query = query.filter(User.dept_id == dept_id)
        
        # 计算总条数
        total_query = select(func.count()).select_from(query.subquery())
        total = await self.db.execute(total_query)
        total = total.scalar()
        
        # 分页查询
        query = query.offset(offset).limit(limit)
        result = await self.db.execute(query)
        items = result.scalars().all()
        
        return items, total
    
    async def create(self, obj_in: UserCreate, password_hash: str) -> User:
        """创建用户（需传入加密后的密码）"""
        # 转换为字典并添加加密后的密码
        db_obj = User(
            **obj_in.dict(exclude={"password"}),
            password=password_hash
        )
        self.db.add(db_obj)
        await self.db.commit()
        await self.db.refresh(db_obj)  # 刷新获取数据库生成的字段（如id、创建时间）
        return db_obj
    
    async def update(self, db_obj: User, obj_in: UserUpdate, password_hash: Optional[str] = None) -> User:
        """更新用户（支持密码更新）"""
        obj_data = obj_in.dict(exclude_unset=True)  # 只更新传入的字段
        for field in obj_data:
            if field == "password":
                continue  # 密码单独处理
            setattr(db_obj, field, obj_data[field])
        
        # 如果传入了新密码，更新密码
        if password_hash:
            db_obj.password = password_hash
        
        await self.db.commit()
        await self.db.refresh(db_obj)
        return db_obj
    
    async def delete(self, user_id: int) -> bool:
        """删除用户（物理删除，逻辑删除可改为更新status）"""
        user = await self.get_by_id(user_id)
        if not user:
            return False
        await self.db.delete(user)
        await self.db.commit()
        return True
    
    async def update_last_login(self, user_id: int) -> bool:
        """更新最后登录时间"""
        user = await self.get_by_id(user_id)
        if not user:
            return False
        from datetime import datetime
        user.last_login_time = datetime.now()
        await self.db.commit()
        return True
    
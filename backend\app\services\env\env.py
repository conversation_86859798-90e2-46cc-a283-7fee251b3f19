"""
环境管理服务层
处理环境管理相关的业务逻辑
"""
import time
from typing import List, Tuple, Optional, Dict, Any, Type
from sqlalchemy.ext.asyncio import AsyncSession

from app.services.base import BaseService
from app.repositories.env.env import EnvironmentRepository
from app.repositories.user.user import UserRepository
from app.schemas.env.env import EnvironmentCreate, EnvironmentUpdate, EnvironmentResponse, EnvironmentQuery, EnvironmentPageResponse, ConnectionTestRequest, ConnectionTestResponse
from app.models.env.env import Environment
from app.core.config_manager import config_manager
from app.core.exceptions import raise_env_not_found, raise_business_error
from app.core.error_codes import ErrorCode
from app.utils.logger import operation_logger


class EnvironmentService(BaseService[Environment, EnvironmentCreate, EnvironmentUpdate, EnvironmentResponse]):
    """环境管理业务服务"""

    def __init__(self, db: AsyncSession):
        repository = EnvironmentRepository(db)
        super().__init__(db, repository)

        # 其他仓库
        self.user_repository = UserRepository(db)

        # 配置管理器
        self.config_manager = config_manager
        self.client_factory = config_manager.get_client_factory()

    @property
    def model_class(self):
        """返回模型类"""
        return Environment

    @property
    def response_schema_class(self):
        """返回响应Schema类"""
        return EnvironmentResponse

    # ==================== BaseService钩子方法重写 ====================

    async def _validate_before_create(self, create_data: EnvironmentCreate, **_kwargs) -> None:
        """创建环境前的验证"""
        # 检查环境名称是否重复
        existing_env = await self.repository.get_by_name(create_data.name)
        if existing_env:
            raise_business_error(ErrorCode.ENV_NAME_ALREADY_EXISTS, f"环境名称 '{create_data.name}' 已存在")

    def _convert_to_response(self, environment: Environment) -> EnvironmentResponse:
        """将Environment对象转换为EnvironmentResponse"""
        return EnvironmentResponse(
            id=environment.id,
            name=environment.name,
            type=environment.type,
            description=environment.description,
            host=environment.host,
            port=environment.port,
            config=environment.config,
            tags=environment.tags,
            status=environment.status,
            last_test_time=environment.last_test_time,
            created_at=environment.created_at.strftime("%Y-%m-%d %H:%M:%S") if environment.created_at else None,
            updated_at=environment.updated_at.strftime("%Y-%m-%d %H:%M:%S") if environment.updated_at else None,
        )

    # ==================== 业务方法使用基类通用方法 ====================

    async def create_environment(self, env_data: EnvironmentCreate, current_user_id: str) -> EnvironmentResponse:
        """创建环境 - 使用基类通用方法"""
        return await self.create(env_data, current_user_id)

    async def list_environments(self, query: EnvironmentQuery, current_user_id: Optional[int] = None) -> EnvironmentPageResponse:
        """查询环境列表，支持关键词搜索、类型筛选和分页"""
        start_time = time.time()

        try:
            environments, total = await self.repository.list(
                keyword=query.keyword,
                env_type=query.env_type,
                status=query.status,
                tags=query.tags,
                offset=query.offset,
                limit=query.size
            )

            # 批量转换为响应格式（优化性能）
            env_responses = await self._batch_convert_to_response(environments)

            # 计算总页数
            pages = (total + query.size - 1) // query.size

            # 记录查询日志
            duration_ms = (time.time() - start_time) * 1000
            await operation_logger.log_operation(
                operation="env_list_query",
                user_id=current_user_id,
                username=f"user_{current_user_id}" if current_user_id else "anonymous",
                request_data={
                    "keyword": query.keyword,
                    "env_type": query.env_type,
                    "status": query.status,
                    "page": query.page,
                    "size": query.size,
                    "total_found": total
                },
                result="success",
                duration_ms=duration_ms
            )

            return EnvironmentPageResponse(
                items=env_responses,
                total=total,
                page=query.page,
                size=query.size,
                pages=pages
            )

        except Exception as e:
            # 记录查询失败日志
            duration_ms = (time.time() - start_time) * 1000
            await operation_logger.log_operation(
                operation="env_list_query",
                user_id=current_user_id,
                username=f"user_{current_user_id}" if current_user_id else "anonymous",
                request_data=query.model_dump(),
                result="failed",
                error_msg=str(e),
                duration_ms=duration_ms
            )
            raise

    async def get_environment_by_id(self, env_id: int) -> EnvironmentResponse:
        """获取环境详情 - 使用基类通用方法"""
        return await self.get_by_id(env_id)

    async def update_environment(self, env_id: int, env_data: EnvironmentUpdate, current_user_id: str) -> EnvironmentResponse:
        """更新环境 - 使用基类通用方法"""
        return await self.update(env_id, env_data, current_user_id)

    async def delete_environment(self, env_id: int) -> None:
        """删除环境 - 使用基类通用方法"""
        await self.delete(env_id)

    # ==================== 配置管理相关方法 ====================

    @property
    def supported_types(self) -> set:
        """获取支持的环境类型"""
        return set(self.config_manager.get_supported_types())

    # ==================== 连接测试相关方法 ====================

    async def test_environment_connection(self, env_id: int, test_request: ConnectionTestRequest, current_user_id: Optional[int] = None) -> ConnectionTestResponse:
        """测试指定环境的连接"""
        from datetime import datetime
        start_time = time.time()

        try:
            # 获取环境信息
            environment = await self.repository.get(env_id)
            if not environment:
                raise_env_not_found(env_id)

            # 构建测试请求
            base_config = {
                'host': environment.host,
                'port': environment.port,
            }

            # 安全地合并配置
            if environment.config:
                base_config.update(environment.config)

            if test_request.config:
                base_config.update(test_request.config)

            connection_test_request = ConnectionTestRequest(
                type=environment.type,
                config=base_config,
                timeout=test_request.timeout
            )

            # 调用通用测试方法
            result = await self.test_connection(connection_test_request)

            # 更新环境状态和最后测试时间
            try:
                # 使用北京时间
                from datetime import timezone, timedelta
                beijing_tz = timezone(timedelta(hours=8))
                beijing_time = datetime.now(beijing_tz)

                update_data = {
                    'status': 'connected' if result.success else 'failed',
                    'last_test_time': beijing_time,
                    'updated_at': beijing_time
                }

                await self.repository.update(db_obj=environment, obj_in=update_data)

            except Exception as e:
                # 记录更新失败，但不影响测试结果返回
                import logging
                logger = logging.getLogger(__name__)
                logger.warning(f"更新环境状态失败: {str(e)}", exc_info=True)

            # 记录连接测试日志
            duration_ms = (time.time() - start_time) * 1000
            await operation_logger.log_operation(
                operation="env_connection_test",
                user_id=current_user_id,
                username=f"user_{current_user_id}" if current_user_id else "system",
                request_data={
                    "env_id": env_id,
                    "env_name": environment.name,
                    "env_type": environment.type,
                    "test_success": result.success,
                    "test_duration": result.duration
                },
                result="success" if result.success else "failed",
                error_msg=result.message if not result.success else None,
                duration_ms=duration_ms
            )

            return result

        except Exception as e:
            # 记录测试失败日志
            duration_ms = (time.time() - start_time) * 1000
            await operation_logger.log_operation(
                operation="env_connection_test",
                user_id=current_user_id,
                username=f"user_{current_user_id}" if current_user_id else "system",
                request_data={"env_id": env_id},
                result="failed",
                error_msg=str(e),
                duration_ms=duration_ms
            )
            raise

    async def test_connection(self, test_request: ConnectionTestRequest) -> ConnectionTestResponse:
        """测试环境连接"""

        # 使用客户端工厂创建客户端
        try:
            client = self.client_factory.create_client(test_request.type, test_request.config)
            result = await client.test_connection()

            return ConnectionTestResponse(
                success=result.success,
                message=result.message,
                duration=result.duration,
                details=result.details
            )
        except Exception as e:
            # 连接测试失败应该返回失败响应，而不是抛出异常
            return ConnectionTestResponse(
                success=False,
                message=f"连接测试失败: {str(e)}",
                duration=0,
                details={"error": str(e), "error_type": type(e).__name__}
            )

    async def batch_test_connections(self, env_ids: List[int]) -> List[Dict[str, Any]]:
        """批量测试环境连接"""
        
        results = []
        environments = await self.repository.get_environments_by_ids(env_ids)
        
        for env in environments:
            try:
                test_request = ConnectionTestRequest(
                    type=env.type,
                    config={
                        'host': env.host,
                        'port': env.port,
                        **env.config
                    }
                )
                
                result = await self.test_connection(test_request)
                
                results.append({
                    'id': env.id,
                    'name': env.name,
                    'success': result.success,
                    'message': result.message,
                    'duration': result.duration  # 修正字段名，与ConnectionTestResponse保持一致
                })
                
                # 更新连接状态和测试时间
                await self.repository.update_status(
                    env.id, 
                    'active' if result.success else 'inactive'
                )
                
            except Exception as e:
                results.append({
                    'id': env.id,
                    'name': env.name,
                    'success': False,
                    'message': f"测试失败: {str(e)}",
                    'duration': 0  # 修正字段名，与ConnectionTestResponse保持一致
                })
        
        return results

    async def get_environment_stats(self) -> Dict[str, Any]:
        """获取环境统计信息"""
        try:
            # 基础类型统计
            type_stats = await self.repository.get_env_types_stats()

            # 状态统计
            status_stats = await self.repository.get_status_stats()

            # 详细类型统计（包含状态分布）
            detailed_type_stats = await self.repository.get_detailed_type_stats()

            # 最近环境
            recent_envs = await self.repository.get_recent_environments(limit=5)
            recent_env_responses = [EnvironmentResponse.model_validate(env) for env in recent_envs]

            return {
                'type_stats': type_stats,
                'status_stats': status_stats,
                'detailed_type_stats': detailed_type_stats,
                'recent_environments': recent_env_responses
            }
        except Exception as e:
            # 统计信息获取失败，使用统一错误码
            raise_business_error(
                ErrorCode.OPERATION_FAILED,
                f"获取环境统计信息失败: {str(e)}",
                details={"error": str(e)}
            )

    async def get_supported_types(self) -> List[Dict[str, Any]]:
        """获取支持的环境类型详细信息"""
        try:
            configs = self.config_manager.get_all_environment_configs()
            return [
                {
                    'type': config.name,
                    'display_name': config.display_name,
                    'description': config.description,
                    'default_port': config.default_port,
                    'required_fields': config.required_fields,
                    'optional_fields': config.optional_fields,
                    'icon': config.icon,
                    'category': config.category
                }
                for config in configs.values()
            ]
        except Exception as e:
            # 获取支持类型失败，使用统一错误码
            raise_business_error(
                ErrorCode.CONFIGURATION_ERROR,
                f"获取支持的环境类型失败: {str(e)}",
                details={"error": str(e)}
            )



    async def _convert_to_response_with_user_info(self, obj: Environment) -> EnvironmentResponse:
        """
        将环境模型对象转换为响应对象，包含用户昵称
        """
        # 先使用基础转换
        response_data = obj.__dict__.copy()

        # 查询创建者昵称
        if obj.created_by:
            try:
                # 尝试将created_by作为用户ID查询
                if obj.created_by.isdigit():
                    creator = await self.user_repository.get(int(obj.created_by))
                    if creator and creator.nickname:
                        response_data['created_by'] = creator.nickname
                    elif creator:
                        response_data['created_by'] = creator.username
                else:
                    # 如果不是数字ID，尝试作为用户名查询
                    creator = await self.user_repository.get_by_username(obj.created_by)
                    if creator and creator.nickname:
                        response_data['created_by'] = creator.nickname
                    elif creator:
                        response_data['created_by'] = creator.username
            except Exception:
                # 查询失败时保持原值
                pass

        # 查询更新者昵称
        if obj.updated_by:
            try:
                # 尝试将updated_by作为用户ID查询
                if obj.updated_by.isdigit():
                    updater = await self.user_repository.get(int(obj.updated_by))
                    if updater and updater.nickname:
                        response_data['updated_by'] = updater.nickname
                    elif updater:
                        response_data['updated_by'] = updater.username
                else:
                    # 如果不是数字ID，尝试作为用户名查询
                    updater = await self.user_repository.get_by_username(obj.updated_by)
                    if updater and updater.nickname:
                        response_data['updated_by'] = updater.nickname
                    elif updater:
                        response_data['updated_by'] = updater.username
            except Exception:
                # 查询失败时保持原值
                pass

        # 创建响应对象
        return EnvironmentResponse(**response_data)

    async def _batch_convert_to_response(self, environments: List[Environment]) -> List[EnvironmentResponse]:
        """批量转换环境对象为响应模型（优化性能，避免N+1查询）"""
        if not environments:
            return []

        # 使用基类的通用方法获取用户信息映射
        user_map = await self._get_user_info_map(environments)

        # 转换所有环境
        env_responses = []
        for env in environments:
            # 使用基类通用方法获取用户名称
            created_by_name = self._get_user_display_name(env.created_by, user_map)
            updated_by_name = self._get_user_display_name(env.updated_by, user_map)

            # 创建响应对象
            response = EnvironmentResponse(
                id=env.id,
                name=env.name,
                type=env.type,
                description=env.description,
                host=env.host,
                port=env.port,
                config=env.config,
                tags=env.tags,
                status=env.status,
                last_test_time=env.last_test_time,
                created_at=env.created_at.strftime("%Y-%m-%d %H:%M:%S") if env.created_at else None,
                updated_at=env.updated_at.strftime("%Y-%m-%d %H:%M:%S") if env.updated_at else None,
                created_by=created_by_name,
                updated_by=updated_by_name
            )
            env_responses.append(response)

        return env_responses




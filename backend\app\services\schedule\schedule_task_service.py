"""
定时任务配置服务
"""
import json
import logging
from datetime import datetime
from typing import Dict, Any, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text

logger = logging.getLogger(__name__)


class ScheduleTaskService:
    """定时任务配置服务"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create_chaos_schedule_task(
        self,
        chaos_task_id: int,
        chaos_task_name: str,
        execution_type: str,
        created_by: int,
        scheduled_time: Optional[datetime] = None,
        cron_expression: Optional[str] = None,
        periodic_config: Optional[Dict[str, Any]] = None
    ) -> str:
        """为混沌测试任务创建调度任务配置"""
        try:
            task_id = f"chaos_task_{chaos_task_id}"

            # 根据执行类型准备配置数据
            if execution_type == "scheduled":
                schedule_type = "date"
                schedule_config = {
                    "run_date": scheduled_time.isoformat()
                }
            elif execution_type == "cron":
                schedule_type = "cron"
                # 解析cron表达式
                cron_parts = cron_expression.strip().split()
                if len(cron_parts) == 5:
                    # 标准5位cron: 分 时 日 月 周
                    schedule_config = {
                        "minute": cron_parts[0],
                        "hour": cron_parts[1],
                        "day": cron_parts[2],
                        "month": cron_parts[3],
                        "day_of_week": cron_parts[4]
                    }
                elif len(cron_parts) == 6:
                    # 6位cron: 秒 分 时 日 月 周
                    schedule_config = {
                        "second": cron_parts[0],
                        "minute": cron_parts[1],
                        "hour": cron_parts[2],
                        "day": cron_parts[3],
                        "month": cron_parts[4],
                        "day_of_week": cron_parts[5]
                    }
                else:
                    raise ValueError("Cron表达式格式错误")
            elif execution_type == "periodic":
                schedule_type = "interval"
                schedule_config = periodic_config or {}
            else:
                raise ValueError(f"不支持的执行类型: {execution_type}")

            function_args = [chaos_task_id]
            function_kwargs = {}
            
            # 插入定时任务配置
            query = text("""
                INSERT INTO schedule_task_config 
                (task_id, task_name, task_type, description, schedule_type, schedule_config,
                 function_path, function_args, function_kwargs, max_instances, 
                 misfire_grace_time, is_active, created_by, created_at, updated_at)
                VALUES 
                (:task_id, :task_name, :task_type, :description, :schedule_type, :schedule_config,
                 :function_path, :function_args, :function_kwargs, :max_instances,
                 :misfire_grace_time, :is_active, :created_by, :created_at, :updated_at)
            """)
            
            await self.db.execute(query, {
                'task_id': task_id,
                'task_name': f"混沌测试任务: {chaos_task_name}",
                'task_type': 'chaos_execution',
                'description': f"执行混沌测试任务: {chaos_task_name} (ID: {chaos_task_id}) - {execution_type}",
                'schedule_type': schedule_type,
                'schedule_config': json.dumps(schedule_config),
                'function_path': 'app.schedule.tasks.schedule_tasks.execute_schedule_task',
                'function_args': json.dumps(function_args),
                'function_kwargs': json.dumps(function_kwargs),
                'max_instances': 1,
                'misfire_grace_time': 300,
                'is_active': True,
                'created_by': created_by,
                'created_at': datetime.now(),
                'updated_at': datetime.now()
            })
            
            await self.db.commit()
            
            logger.info(f"创建定时任务配置成功: {task_id}")
            return task_id
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"创建定时任务配置失败: {str(e)}")
            raise
    
    async def update_chaos_schedule_task(
        self,
        chaos_task_id: int,
        chaos_task_name: str,
        execution_type: str,
        updated_by: int,
        scheduled_time: Optional[datetime] = None,
        cron_expression: Optional[str] = None,
        periodic_config: Optional[Dict[str, Any]] = None
    ) -> bool:
        """更新混沌测试任务的定时任务配置"""
        try:
            task_id = f"chaos_task_{chaos_task_id}"
            
            # 根据执行类型准备配置数据
            if execution_type == "scheduled":
                schedule_type = "date"
                schedule_config = {
                    "run_date": scheduled_time.isoformat()
                }
            elif execution_type == "cron":
                schedule_type = "cron"
                # 解析cron表达式
                cron_parts = cron_expression.strip().split()
                if len(cron_parts) == 5:
                    schedule_config = {
                        "minute": cron_parts[0],
                        "hour": cron_parts[1],
                        "day": cron_parts[2],
                        "month": cron_parts[3],
                        "day_of_week": cron_parts[4]
                    }
                elif len(cron_parts) == 6:
                    schedule_config = {
                        "second": cron_parts[0],
                        "minute": cron_parts[1],
                        "hour": cron_parts[2],
                        "day": cron_parts[3],
                        "month": cron_parts[4],
                        "day_of_week": cron_parts[5]
                    }
                else:
                    raise ValueError("Cron表达式格式错误")
            elif execution_type == "periodic":
                schedule_type = "interval"
                schedule_config = periodic_config or {}
            else:
                raise ValueError(f"不支持的执行类型: {execution_type}")

            # 更新定时任务配置
            query = text("""
                UPDATE schedule_task_config
                SET task_name = :task_name,
                    description = :description,
                    schedule_type = :schedule_type,
                    schedule_config = :schedule_config,
                    updated_at = :updated_at
                WHERE task_id = :task_id
            """)

            result = await self.db.execute(query, {
                'task_id': task_id,
                'task_name': f"混沌测试任务: {chaos_task_name}",
                'description': f"执行混沌测试任务: {chaos_task_name} (ID: {chaos_task_id}) - {execution_type}",
                'schedule_type': schedule_type,
                'schedule_config': json.dumps(schedule_config),
                'updated_at': datetime.now()
            })
            
            await self.db.commit()
            
            if result.rowcount > 0:
                logger.info(f"更新定时任务配置成功: {task_id}")
                return True
            else:
                logger.warning(f"未找到要更新的定时任务配置: {task_id}")
                return False
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"更新定时任务配置失败: {str(e)}")
            raise

    async def delete_chaos_schedule_task(self, chaos_task_id: int, deleted_by: int) -> bool:
        """删除混沌测试任务的定时任务配置"""
        try:
            task_id = f"chaos_task_{chaos_task_id}"

            # 1. 先从调度器中移除作业
            try:
                from app.schedule.schedule_manager import remove_job
                success = remove_job(task_id)
                if success:
                    logger.info(f"从调度器中移除作业成功: {task_id}")
                else:
                    logger.debug(f"调度器作业 {task_id} 不存在或已移除")
            except Exception as e:
                if "was not found" in str(e) or "No job by the id" in str(e):
                    logger.debug(f"调度器作业 {task_id} 不存在或已移除")
                else:
                    logger.warning(f"从调度器中移除作业失败: {task_id}, 错误: {str(e)}")

            # 2. 删除调度器作业表中的记录
            try:
                job_query = text("""
                    DELETE FROM schedule_jobs
                    WHERE id = :task_id
                """)
                await self.db.execute(job_query, {'task_id': task_id})
                logger.info(f"删除调度器作业记录成功: {task_id}")
            except Exception as e:
                logger.warning(f"删除调度器作业记录失败: {task_id}, 错误: {str(e)}")

            # 3. 删除定时任务配置
            query = text("""
                DELETE FROM schedule_task_config
                WHERE task_id = :task_id
            """)

            result = await self.db.execute(query, {'task_id': task_id})
            await self.db.commit()

            if result.rowcount > 0:
                logger.info(f"删除定时任务配置成功: {task_id}")
                return True
            else:
                logger.warning(f"定时任务配置不存在: {task_id}")
                return False

        except Exception as e:
            await self.db.rollback()
            logger.error(f"删除定时任务配置失败: {str(e)}")
            raise
    

    
    async def deactivate_chaos_schedule_task(self, chaos_task_id: int) -> bool:
        """停用混沌测试任务的定时任务配置"""
        try:
            task_id = f"chaos_task_{chaos_task_id}"

            # 1. 先从调度器中移除作业
            try:
                from app.schedule.schedule_manager import remove_job
                success = remove_job(task_id)
                if success:
                    logger.info(f"从调度器中移除作业成功: {task_id}")
                else:
                    logger.debug(f"调度器作业 {task_id} 不存在或已移除")
            except Exception as e:
                if "was not found" in str(e) or "No job by the id" in str(e):
                    logger.debug(f"调度器作业 {task_id} 不存在或已移除")
                else:
                    logger.warning(f"从调度器中移除作业失败: {task_id}, 错误: {str(e)}")

            # 2. 删除调度器作业表中的记录
            try:
                job_query = text("""
                    DELETE FROM schedule_jobs
                    WHERE id = :task_id
                """)
                await self.db.execute(job_query, {'task_id': task_id})
                logger.info(f"删除调度器作业记录成功: {task_id}")
            except Exception as e:
                logger.warning(f"删除调度器作业记录失败: {task_id}, 错误: {str(e)}")

            # 3. 停用定时任务配置（保留配置，只是标记为不活跃）
            query = text("""
                UPDATE schedule_task_config
                SET is_active = 0, updated_at = :updated_at
                WHERE task_id = :task_id
            """)

            result = await self.db.execute(query, {
                'task_id': task_id,
                'updated_at': datetime.now()
            })

            await self.db.commit()

            if result.rowcount > 0:
                logger.info(f"停用定时任务配置成功: {task_id}")
                return True
            else:
                logger.warning(f"未找到要停用的定时任务配置: {task_id}")
                return False

        except Exception as e:
            await self.db.rollback()
            logger.error(f"停用定时任务配置失败: {str(e)}")
            raise

    async def activate_chaos_schedule_task(self, chaos_task_id: int) -> bool:
        """激活混沌测试任务的定时任务配置"""
        try:
            task_id = f"chaos_task_{chaos_task_id}"

            # 激活定时任务配置
            query = text("""
                UPDATE schedule_task_config
                SET is_active = 1, updated_at = :updated_at
                WHERE task_id = :task_id
            """)

            result = await self.db.execute(query, {
                'task_id': task_id,
                'updated_at': datetime.now()
            })

            await self.db.commit()

            if result.rowcount > 0:
                logger.info(f"激活定时任务配置成功: {task_id}")
                return True
            else:
                logger.warning(f"未找到要激活的定时任务配置: {task_id}")
                return False

        except Exception as e:
            await self.db.rollback()
            logger.error(f"激活定时任务配置失败: {str(e)}")
            raise
    
    async def create_chaos_batch_schedule_task(
        self,
        batch_task_id: int,
        batch_task_name: str,
        execution_type: str,
        created_by: int,
        scheduled_time: Optional[datetime] = None,
        cron_expression: Optional[str] = None,
        periodic_config: Optional[Dict[str, Any]] = None
    ) -> str:
        """为混沌测试批次任务创建调度任务配置"""
        try:
            task_id = f"chaos_batch_task_{batch_task_id}"

            # 根据执行类型准备配置数据
            if execution_type == "scheduled":
                schedule_type = "date"
                schedule_config = {
                    "run_date": scheduled_time.isoformat()
                }
            elif execution_type == "cron":
                schedule_type = "cron"
                # 解析cron表达式
                cron_parts = cron_expression.strip().split()
                if len(cron_parts) == 5:
                    # 标准5位cron: 分 时 日 月 周
                    schedule_config = {
                        "minute": cron_parts[0],
                        "hour": cron_parts[1],
                        "day": cron_parts[2],
                        "month": cron_parts[3],
                        "day_of_week": cron_parts[4]
                    }
                elif len(cron_parts) == 6:
                    # 6位cron: 秒 分 时 日 月 周
                    schedule_config = {
                        "second": cron_parts[0],
                        "minute": cron_parts[1],
                        "hour": cron_parts[2],
                        "day": cron_parts[3],
                        "month": cron_parts[4],
                        "day_of_week": cron_parts[5]
                    }
                else:
                    raise ValueError("Cron表达式格式错误")
            elif execution_type == "periodic":
                schedule_type = "interval"
                schedule_config = periodic_config or {}
            else:
                raise ValueError(f"不支持的执行类型: {execution_type}")

            function_args = [batch_task_id]
            function_kwargs = {}

            # 插入定时任务配置
            query = text("""
                INSERT INTO schedule_task_config
                (task_id, task_name, task_type, description, schedule_type, schedule_config,
                 function_path, function_args, function_kwargs, max_instances,
                 misfire_grace_time, is_active, created_by, created_at, updated_at)
                VALUES
                (:task_id, :task_name, :task_type, :description, :schedule_type, :schedule_config,
                 :function_path, :function_args, :function_kwargs, :max_instances,
                 :misfire_grace_time, :is_active, :created_by, :created_at, :updated_at)
            """)

            await self.db.execute(query, {
                'task_id': task_id,
                'task_name': f"混沌测试批次任务: {batch_task_name}",
                'task_type': 'chaos_batch_execution',
                'description': f"执行混沌测试批次任务: {batch_task_name} (ID: {batch_task_id}) - {execution_type}",
                'schedule_type': schedule_type,
                'schedule_config': json.dumps(schedule_config),
                'function_path': 'app.schedule.tasks.schedule_tasks.execute_schedule_task',
                'function_args': json.dumps(function_args),
                'function_kwargs': json.dumps(function_kwargs),
                'max_instances': 1,
                'misfire_grace_time': 300,
                'is_active': True,
                'created_by': created_by,
                'created_at': datetime.now(),
                'updated_at': datetime.now()
            })

            await self.db.commit()

            logger.info(f"创建批次任务定时配置成功: {task_id}")
            return task_id

        except Exception as e:
            await self.db.rollback()
            logger.error(f"创建批次任务定时配置失败: {str(e)}")
            raise

    async def update_chaos_batch_schedule_task(
        self,
        batch_task_id: int,
        batch_task_name: str,
        execution_type: str,
        updated_by: int,
        scheduled_time: Optional[datetime] = None,
        cron_expression: Optional[str] = None,
        periodic_config: Optional[Dict[str, Any]] = None
    ) -> bool:
        """更新混沌测试批次任务的定时任务配置"""
        try:
            task_id = f"chaos_batch_task_{batch_task_id}"

            # 根据执行类型准备配置数据
            if execution_type == "scheduled":
                schedule_type = "date"
                schedule_config = {
                    "run_date": scheduled_time.isoformat()
                }
            elif execution_type == "cron":
                schedule_type = "cron"
                # 解析cron表达式
                cron_parts = cron_expression.strip().split()
                if len(cron_parts) == 5:
                    schedule_config = {
                        "minute": cron_parts[0],
                        "hour": cron_parts[1],
                        "day": cron_parts[2],
                        "month": cron_parts[3],
                        "day_of_week": cron_parts[4]
                    }
                elif len(cron_parts) == 6:
                    schedule_config = {
                        "second": cron_parts[0],
                        "minute": cron_parts[1],
                        "hour": cron_parts[2],
                        "day": cron_parts[3],
                        "month": cron_parts[4],
                        "day_of_week": cron_parts[5]
                    }
                else:
                    raise ValueError("Cron表达式格式错误")
            elif execution_type == "periodic":
                schedule_type = "interval"
                schedule_config = periodic_config or {}
            else:
                raise ValueError(f"不支持的执行类型: {execution_type}")

            # 更新定时任务配置
            query = text("""
                UPDATE schedule_task_config
                SET task_name = :task_name,
                    description = :description,
                    schedule_type = :schedule_type,
                    schedule_config = :schedule_config,
                    updated_at = :updated_at
                WHERE task_id = :task_id
            """)

            result = await self.db.execute(query, {
                'task_id': task_id,
                'task_name': f"混沌测试批次任务: {batch_task_name}",
                'description': f"执行混沌测试批次任务: {batch_task_name} (ID: {batch_task_id}) - {execution_type}",
                'schedule_type': schedule_type,
                'schedule_config': json.dumps(schedule_config),
                'updated_at': datetime.now()
            })

            await self.db.commit()

            if result.rowcount > 0:
                logger.info(f"更新批次任务定时配置成功: {task_id}")
                return True
            else:
                logger.warning(f"未找到要更新的批次任务定时配置: {task_id}")
                return False

        except Exception as e:
            await self.db.rollback()
            logger.error(f"更新批次任务定时配置失败: {str(e)}")
            raise

    async def get_schedule_task_config(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取定时任务配置"""
        try:
            query = text("""
                SELECT task_id, task_name, task_type, description, schedule_type,
                       schedule_config, function_path, function_args, function_kwargs,
                       max_instances, misfire_grace_time, is_active, created_by,
                       created_at, updated_at
                FROM schedule_task_config
                WHERE task_id = :task_id
            """)

            result = await self.db.execute(query, {'task_id': task_id})
            row = result.fetchone()

            if row:
                return {
                    'task_id': row.task_id,
                    'task_name': row.task_name,
                    'task_type': row.task_type,
                    'description': row.description,
                    'schedule_type': row.schedule_type,
                    'schedule_config': json.loads(row.schedule_config) if row.schedule_config else {},
                    'function_path': row.function_path,
                    'function_args': json.loads(row.function_args) if row.function_args else [],
                    'function_kwargs': json.loads(row.function_kwargs) if row.function_kwargs else {},
                    'max_instances': row.max_instances,
                    'misfire_grace_time': row.misfire_grace_time,
                    'is_active': bool(row.is_active),
                    'created_by': row.created_by,
                    'created_at': row.created_at,
                    'updated_at': row.updated_at
                }

            return None

        except Exception as e:
            logger.error(f"获取定时任务配置失败: {str(e)}")
            raise

/**
 * 路由别名，方便快速找到页面，同时可以用作路由跳转
 */
export enum RoutesAlias {
  // 布局和认证
  Layout = '/index/index', // 布局容器
  Login = '/auth/login', // 登录
  Register = '/auth/register', // 注册
  ForgetPassword = '/auth/forget-password', // 忘记密码

  // 异常页面
  Exception403 = '/exception/403', // 403
  Exception404 = '/exception/404', // 404
  Exception500 = '/exception/500', // 500

  // 结果页面
  Success = '/result/success', // 成功
  Fail = '/result/fail', // 失败

  // 仪表板
  Dashboard = '/dashboard/console', // 工作台

  // 系统管理
  User = '/system/user', // 账户
  Role = '/system/role', // 角色
  UserCenter = '/system/user-center', // 用户中心
  Menu = '/system/menu', // 菜单

  // 通用配置
  Environment = '/config/environment', // 环境管理
  ModelConfig = '/config/model', // 模型管理

  // 混沌测试
  ChaosIndex = '/chaos/index', // 混沌测试主页
  ChaosMonitor = '/chaos/monitor', // 主机监控
  ChaosTasks = '/chaos/tasks/index', // 任务管理
  ChaosTaskFormCreate = '/chaos/tasks/form', // 任务表单（创建/编辑）
  ChaosTaskFormEdit = '/chaos/tasks/form', // 任务表单（创建/编辑）
  ChaosBatchTaskFormCreate = '/chaos/tasks/batch-form', // 批次任务表单（创建/编辑）
  ChaosBatchTaskFormEdit = '/chaos/tasks/batch-form', // 批次任务表单（创建/编辑）
  ChaosBatchTaskDetail = '/chaos/tasks/batch-detail', // 批次任务详情

  ChaosTaskDetail = '/chaos/tasks/detail', // 任务详情
  ChaosScenarios = '/chaos/scenarios/index', // 场景管理
  ChaosScenarioCreate = '/chaos/scenarios/create', // 场景创建
  ChaosExecutions = '/chaos/executions/index', // 执行记录
  ChaosExecutionDetail = '/chaos/executions/detail', // 执行详情
  ChaosBlade = '/chaos/blade/index', // ChaosBlade管理

  // 数据工厂
  DataFactoryModels = '/data-factory/models/index', // 数据模型管理
  DataFactoryModelFormCreate = '/data-factory/models/form', // 数据模型表单（创建/编辑）
  DataFactoryModelFormEdit = '/data-factory/models/form', // 数据模型表单（创建/编辑）

  DataFactoryTasks = '/data-factory/tasks/index', // 生成任务管理
  DataFactoryTaskCreate = '/data-factory/tasks/create' // 创建生成任务
}

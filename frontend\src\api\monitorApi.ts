/**
 * 混沌测试监控API
 */
import api from '@/utils/http'

// 监控配置接口
export interface MonitorConfig {
  enable_monitoring: boolean
  monitor_duration: 'auto' | 'custom'
  custom_monitor_duration?: number
  collection_interval: number
  metrics: string[]
}

// 监控状态接口
export interface MonitorStatus {
  environment_id: number
  host_id: number
  status: 'not_started' | 'running' | 'stopped' | 'error'
  start_time?: string
  data_points: number
  last_collection_time?: string
}

// 图表数据接口
export interface ChartData {
  metric_type: string
  metric_name: string
  unit: string
  data: number[]
  timestamps: string[]
  current_value: number
  avg_value: number
  max_value: number
  min_value: number
}

// 监控数据响应接口
export interface MonitorDataResponse {
  environment_id: number
  host_id: number
  host_info: any
  monitor_status: MonitorStatus
  charts: ChartData[]
  summary: any
}

// 启动监控请求
export interface StartMonitorRequest {
  environment_id: number
  host_id: number
  config?: MonitorConfig
}

// 停止监控请求
export interface StopMonitorRequest {
  environment_id: number
  host_id: number
}

export class MonitorService {
  /**
   * 启动监控
   */
  static async startMonitoring(requestData: StartMonitorRequest): Promise<MonitorStatus> {
    const response = await request.post<MonitorStatus>({
      url: '/api/chaos/monitor/start',
      data: requestData
    })
    return response
  }

  /**
   * 停止监控
   */
  static async stopMonitoring(requestData: StopMonitorRequest): Promise<boolean> {
    const response = await request.post<boolean>({
      url: '/api/chaos/monitor/stop',
      data: requestData
    })
    return response
  }

  /**
   * 获取监控状态
   */
  static async getMonitorStatus(environmentId: number, hostId: number): Promise<MonitorStatus> {
    const response = await request.get<MonitorStatus>({
      url: `/api/chaos/monitor/${environmentId}/status`,
      params: { host_id: hostId }
    })
    return response
  }

  /**
   * 获取监控数据
   */
  static async getMonitorData(
    environmentId: number,
    hostId: number,
    hours: number = 1
  ): Promise<MonitorDataResponse> {
    const response = await api.restful.get<MonitorDataResponse>({
      url: `/api/chaos/monitor/${environmentId}/data`,
      params: {
        host_id: hostId,
        hours
      }
    })
    return response
  }

  /**
   * 清理过期数据
   */
  static async cleanupOldData(hours: number = 24): Promise<number> {
    const response = await api.restful.post<number>({
      url: '/api/chaos/monitor/cleanup',
      data: { hours }
    })
    return response
  }

  /**
   * 获取默认监控配置
   */
  static getDefaultConfig(): MonitorConfig {
    return {
      enable_monitoring: true,
      monitor_duration: 'auto',
      collection_interval: 30,
      metrics: ['cpu', 'memory', 'network_rx', 'disk_usage']
    }
  }

  /**
   * 格式化监控状态文本
   */
  static formatStatusText(status: string): string {
    const statusMap: Record<string, string> = {
      'not_started': '未开始',
      'running': '监控中',
      'stopped': '已停止',
      'error': '异常'
    }
    return statusMap[status] || '未知'
  }

  /**
   * 获取状态标签类型
   */
  static getStatusTagType(status: string): string {
    const typeMap: Record<string, string> = {
      'not_started': 'info',
      'running': 'success',
      'stopped': 'warning',
      'error': 'danger'
    }
    return typeMap[status] || 'info'
  }

  /**
   * 格式化指标值
   */
  static formatMetricValue(value: number, unit: string): string {
    if (unit === '%') {
      return `${value.toFixed(1)}%`
    } else if (unit === 'KB') {
      return `${value.toFixed(1)}KB`
    } else if (unit === 'MB') {
      return `${value.toFixed(1)}MB`
    } else {
      return `${value.toFixed(2)}${unit}`
    }
  }

  /**
   * 获取指标颜色
   */
  static getMetricColor(metricType: string): string {
    const colorMap: Record<string, string> = {
      'cpu': '#409EFF',
      'memory': '#67C23A',
      'network_rx': '#E6A23C',
      'disk_usage': '#F56C6C'
    }
    return colorMap[metricType] || '#909399'
  }
}

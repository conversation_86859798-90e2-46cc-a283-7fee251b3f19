"""
数据生成器
提供各种类型的测试数据生成功能
"""
import uuid
import random
import string
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional
from datetime import datetime, timedelta
from faker import Faker


class BaseGenerator(ABC):
    """数据生成器基类"""
    
    @abstractmethod
    async def generate(self, options: Optional[Dict[str, Any]] = None) -> Any:
        """
        生成数据
        
        Args:
            options: 生成器选项
            
        Returns:
            生成的数据
        """
        pass
    
    @abstractmethod
    def validate_options(self, options: Dict[str, Any]) -> bool:
        """
        验证生成器选项
        
        Args:
            options: 生成器选项
            
        Returns:
            是否有效
        """
        pass


class UUIDGenerator(BaseGenerator):
    """UUID生成器"""
    
    async def generate(self, options: Optional[Dict[str, Any]] = None) -> str:
        """生成UUID"""
        return str(uuid.uuid4())
    
    def validate_options(self, options: Dict[str, Any]) -> bool:
        """UUID生成器不需要选项"""
        return True


class NameGenerator(BaseGenerator):
    """姓名生成器"""
    
    def __init__(self):
        self.faker_cache = {}
    
    async def generate(self, options: Optional[Dict[str, Any]] = None) -> str:
        """生成姓名"""
        locale = options.get('locale', 'zh_CN') if options else 'zh_CN'
        
        # 缓存Faker实例
        if locale not in self.faker_cache:
            self.faker_cache[locale] = Faker(locale)
        
        fake = self.faker_cache[locale]
        return fake.name()
    
    def validate_options(self, options: Dict[str, Any]) -> bool:
        """验证选项"""
        if not options:
            return True
        
        locale = options.get('locale')
        if locale and not isinstance(locale, str):
            return False
        
        return True


class PhoneGenerator(BaseGenerator):
    """手机号生成器"""
    
    def __init__(self):
        self.faker_cache = {}
    
    async def generate(self, options: Optional[Dict[str, Any]] = None) -> str:
        """生成手机号"""
        locale = options.get('locale', 'zh_CN') if options else 'zh_CN'
        
        if locale not in self.faker_cache:
            self.faker_cache[locale] = Faker(locale)
        
        fake = self.faker_cache[locale]
        return fake.phone_number()
    
    def validate_options(self, options: Dict[str, Any]) -> bool:
        """验证选项"""
        if not options:
            return True
        
        locale = options.get('locale')
        if locale and not isinstance(locale, str):
            return False
        
        return True


class EmailGenerator(BaseGenerator):
    """邮箱生成器"""
    
    def __init__(self):
        self.faker = Faker()
    
    async def generate(self, options: Optional[Dict[str, Any]] = None) -> str:
        """生成邮箱"""
        domain = options.get('domain') if options else None
        
        if domain:
            username = self.faker.user_name()
            return f"{username}@{domain}"
        else:
            return self.faker.email()
    
    def validate_options(self, options: Dict[str, Any]) -> bool:
        """验证选项"""
        if not options:
            return True
        
        domain = options.get('domain')
        if domain and not isinstance(domain, str):
            return False
        
        return True


class RangeGenerator(BaseGenerator):
    """数值范围生成器"""
    
    async def generate(self, options: Optional[Dict[str, Any]] = None) -> int:
        """生成范围内的数值"""
        if not options:
            return random.randint(1, 100)
        
        min_val = options.get('min', 1)
        max_val = options.get('max', 100)
        
        return random.randint(min_val, max_val)
    
    def validate_options(self, options: Dict[str, Any]) -> bool:
        """验证选项"""
        if not options:
            return True
        
        min_val = options.get('min')
        max_val = options.get('max')
        
        if min_val is not None and not isinstance(min_val, int):
            return False
        
        if max_val is not None and not isinstance(max_val, int):
            return False
        
        if min_val is not None and max_val is not None and min_val > max_val:
            return False
        
        return True


class SequenceGenerator(BaseGenerator):
    """序列生成器"""
    
    def __init__(self):
        self.counters = {}
    
    async def generate(self, options: Optional[Dict[str, Any]] = None) -> int:
        """生成序列数值"""
        start = options.get('start', 1) if options else 1
        step = options.get('step', 1) if options else 1
        prefix = options.get('prefix', 'default') if options else 'default'
        
        if prefix not in self.counters:
            self.counters[prefix] = start
        else:
            self.counters[prefix] += step
        
        return self.counters[prefix]
    
    def validate_options(self, options: Dict[str, Any]) -> bool:
        """验证选项"""
        if not options:
            return True
        
        start = options.get('start')
        step = options.get('step')
        prefix = options.get('prefix')
        
        if start is not None and not isinstance(start, int):
            return False
        
        if step is not None and not isinstance(step, int):
            return False
        
        if prefix is not None and not isinstance(prefix, str):
            return False
        
        return True


class DateGenerator(BaseGenerator):
    """日期生成器"""
    
    async def generate(self, options: Optional[Dict[str, Any]] = None) -> str:
        """生成日期"""
        if not options:
            # 默认生成过去一年内的随机日期
            end_date = datetime.now()
            start_date = end_date - timedelta(days=365)
        else:
            start_str = options.get('start', '2020-01-01')
            end_str = options.get('end', datetime.now().strftime('%Y-%m-%d'))
            
            start_date = datetime.strptime(start_str, '%Y-%m-%d')
            end_date = datetime.strptime(end_str, '%Y-%m-%d')
        
        # 生成随机日期
        time_between = end_date - start_date
        days_between = time_between.days
        random_days = random.randrange(days_between)
        random_date = start_date + timedelta(days=random_days)
        
        return random_date.strftime('%Y-%m-%d')
    
    def validate_options(self, options: Dict[str, Any]) -> bool:
        """验证选项"""
        if not options:
            return True
        
        start = options.get('start')
        end = options.get('end')
        
        if start:
            try:
                datetime.strptime(start, '%Y-%m-%d')
            except ValueError:
                return False
        
        if end:
            try:
                datetime.strptime(end, '%Y-%m-%d')
            except ValueError:
                return False
        
        return True


class DataGeneratorEngine:
    """数据生成引擎"""
    
    def __init__(self):
        self.generators = {
            'uuid': UUIDGenerator(),
            'name': NameGenerator(),
            'phone': PhoneGenerator(),
            'email': EmailGenerator(),
            'range': RangeGenerator(),
            'sequence': SequenceGenerator(),
            'date': DateGenerator(),
        }
    
    def is_generator_supported(self, generator_type: str) -> bool:
        """检查生成器是否支持"""
        return generator_type in self.generators
    
    def validate_generator_options(self, generator_type: str, options: Dict[str, Any]) -> bool:
        """验证生成器选项"""
        if generator_type not in self.generators:
            return False
        
        generator = self.generators[generator_type]
        return generator.validate_options(options)
    
    async def generate_data(self, fields_config: List[Dict[str, Any]], count: int) -> List[Dict[str, Any]]:
        """
        根据字段配置生成数据
        
        Args:
            fields_config: 字段配置列表
            count: 生成数据条数
            
        Returns:
            生成的数据列表
        """
        results = []
        
        for i in range(count):
            record = {}
            for field in fields_config:
                field_name = field['name']
                generator_type = field['generator']
                options = field.get('options', {})
                
                if generator_type in self.generators:
                    generator = self.generators[generator_type]
                    record[field_name] = await generator.generate(options)
                else:
                    # 如果生成器不存在，使用默认值
                    record[field_name] = None
            
            results.append(record)
        
        return results

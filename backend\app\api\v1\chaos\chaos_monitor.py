"""
混沌测试监控API路由
"""
from typing import Dict, Any
from fastapi import APIRouter, Depends, Path, Query, Body

from app.api.deps import get_current_user
from app.core.dependencies import get_chaos_monitor_service, get_environment_service
from app.core.responses import response_builder
from app.models.user.user import User
from app.services.chaos.chaos_monitor_service import ChaosMonitorService
from app.services.env.env import EnvironmentService
from app.core.exceptions import raise_not_found, raise_validation_error
from app.schemas.chaos.chaos_monitor import (
    ChaosMonitorStartRequest, ChaosMonitorStopRequest, ChaosMonitorStatusResponse, ChaosMonitorDataResponse, ChaosMonitorConfig
)

router = APIRouter()


@router.post("/start", response_model=ChaosMonitorStatusResponse, summary="启动监控")
async def start_monitoring(
    request: ChaosMonitorStartRequest,
    service: ChaosMonitorService = Depends(get_chaos_monitor_service),
    env_service: EnvironmentService = Depends(get_environment_service),
    current_user: User = Depends(get_current_user)
):
    """启动主机监控"""
    status = await service.start_monitoring_with_validation(
        request.environment_id,
        request.host_id,
        request.config,
        env_service
    )
    return response_builder.success(status)


@router.post("/stop", response_model=bool, summary="停止监控")
async def stop_monitoring(
    request: ChaosMonitorStopRequest,
    service: ChaosMonitorService = Depends(get_chaos_monitor_service),
    current_user: User = Depends(get_current_user)
):
    """停止主机监控"""
    result = await service.stop_monitoring(request.environment_id, request.host_id)
    return response_builder.success(result)


@router.get("/{environment_id}/status", response_model=ChaosMonitorStatusResponse, summary="获取监控状态")
async def get_monitoring_status(
    environment_id: int = Path(..., description="环境ID"),
    host_id: int = Query(..., description="主机ID"),
    service: ChaosMonitorService = Depends(get_chaos_monitor_service),
    current_user: User = Depends(get_current_user)
):
    """获取监控状态"""
    status = await service.get_monitoring_status(environment_id, host_id)
    return response_builder.success(status)


@router.get("/{environment_id}/data", response_model=ChaosMonitorDataResponse, summary="获取监控数据")
async def get_monitor_data(
    environment_id: int = Path(..., description="环境ID"),
    host_id: int = Query(..., description="主机ID"),
    hours: int = Query(1, ge=1, le=24, description="查询最近几小时的数据"),
    service: ChaosMonitorService = Depends(get_chaos_monitor_service),
    current_user: User = Depends(get_current_user)
):
    """获取监控数据"""
    data = await service.get_monitor_data(environment_id, host_id, hours)
    return response_builder.success(data)


@router.post("/cleanup", response_model=int, summary="清理过期数据")
async def cleanup_old_data(
    request: dict = Body(..., description="清理请求参数"),
    service: ChaosMonitorService = Depends(get_chaos_monitor_service),
    current_user: User = Depends(get_current_user)
):
    """清理过期监控数据"""
    hours = request.get("hours", 24)
    deleted_count = await service.cleanup_old_data(hours)
    return response_builder.success(deleted_count)

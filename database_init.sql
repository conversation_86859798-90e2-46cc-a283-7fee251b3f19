-- =====================================================
-- DpTestPlatform 数据库初始化脚本
-- 版本: 1.0.0
-- 创建时间: 2025-01-30
-- 描述: 包含所有表结构和初始化测试数据
-- =====================================================

-- 设置字符集和时区
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;
SET time_zone = '+00:00';

-- =====================================================
-- 1. 用户管理模块
-- =====================================================

-- 用户表
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `email` varchar(100) NOT NULL COMMENT '邮箱',
  `hashed_password` varchar(255) NOT NULL COMMENT '密码哈希',
  `nickname` varchar(50) DEFAULT NULL COMMENT '昵称',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像URL',
  `description` text DEFAULT NULL COMMENT '个人描述',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否激活',
  `is_superuser` tinyint(1) DEFAULT 0 COMMENT '是否超级用户',
  `status` varchar(10) DEFAULT '1' COMMENT '用户状态：1-正常，2-禁用',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建者',
  `updated_by` varchar(50) DEFAULT NULL COMMENT '更新者',
  PRIMARY KEY (`id`),
  UNIQUE KEY `ix_users_username` (`username`),
  UNIQUE KEY `ix_users_email` (`email`),
  KEY `ix_users_id` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 角色表
DROP TABLE IF EXISTS `roles`;
CREATE TABLE `roles` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(50) NOT NULL COMMENT '角色名称',
  `code` varchar(50) NOT NULL COMMENT '角色代码',
  `description` text DEFAULT NULL COMMENT '角色描述',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否激活',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建者',
  `updated_by` varchar(50) DEFAULT NULL COMMENT '更新者',
  PRIMARY KEY (`id`),
  UNIQUE KEY `ix_roles_code` (`code`),
  KEY `ix_roles_id` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色表';

-- 用户角色关联表
DROP TABLE IF EXISTS `user_roles`;
CREATE TABLE `user_roles` (
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `role_id` int(11) NOT NULL COMMENT '角色ID',
  PRIMARY KEY (`user_id`, `role_id`),
  KEY `fk_user_roles_role_id` (`role_id`),
  CONSTRAINT `fk_user_roles_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_user_roles_role_id` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户角色关联表';

-- =====================================================
-- 2. 环境管理模块
-- =====================================================

-- 环境表
DROP TABLE IF EXISTS `environments`;
CREATE TABLE `environments` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(100) NOT NULL COMMENT '环境名称',
  `description` text DEFAULT NULL COMMENT '环境描述',
  `env_type` varchar(20) NOT NULL COMMENT '环境类型：dev-开发，test-测试，staging-预发布，prod-生产',
  `base_url` varchar(255) NOT NULL COMMENT '基础URL',
  `status` varchar(10) DEFAULT '1' COMMENT '状态：1-启用，2-禁用',
  `config` json DEFAULT NULL COMMENT '环境配置（JSON格式）',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建者',
  `updated_by` varchar(50) DEFAULT NULL COMMENT '更新者',
  PRIMARY KEY (`id`),
  UNIQUE KEY `ix_environments_name` (`name`),
  KEY `ix_environments_id` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='环境表';

-- =====================================================
-- 3. 数据工厂模块
-- =====================================================

-- 数据模型表
DROP TABLE IF EXISTS `data_factory_models`;
CREATE TABLE `data_factory_models` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(100) NOT NULL COMMENT '模型名称',
  `description` text DEFAULT NULL COMMENT '模型描述',
  `version` varchar(20) DEFAULT '1.0.0' COMMENT '模型版本',
  `category` varchar(50) DEFAULT NULL COMMENT '模型分类',
  `fields_config` json NOT NULL COMMENT '字段配置（JSON格式）',
  `usage_count` int(11) DEFAULT 0 COMMENT '使用次数',
  `status` varchar(10) DEFAULT '1' COMMENT '状态：1-启用，2-禁用',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建者',
  `updated_by` varchar(50) DEFAULT NULL COMMENT '更新者',
  PRIMARY KEY (`id`),
  UNIQUE KEY `ix_data_factory_models_name` (`name`),
  KEY `ix_data_factory_models_id` (`id`),
  KEY `ix_data_factory_models_category` (`category`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据模型表';

-- 模型配置表
DROP TABLE IF EXISTS `model_configs`;
CREATE TABLE `model_configs` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `model_id` int(11) NOT NULL COMMENT '数据模型ID',
  `config_name` varchar(100) NOT NULL COMMENT '配置名称',
  `config_type` varchar(50) NOT NULL COMMENT '配置类型：generation-生成配置，validation-验证配置，export-导出配置',
  `config_data` json NOT NULL COMMENT '配置数据（JSON格式）',
  `is_default` tinyint(1) DEFAULT 0 COMMENT '是否为默认配置',
  `description` text DEFAULT NULL COMMENT '配置描述',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建者',
  `updated_by` varchar(50) DEFAULT NULL COMMENT '更新者',
  PRIMARY KEY (`id`),
  KEY `ix_model_configs_id` (`id`),
  KEY `ix_model_configs_model_id` (`model_id`),
  KEY `ix_model_configs_config_type` (`config_type`),
  CONSTRAINT `fk_model_configs_model_id` FOREIGN KEY (`model_id`) REFERENCES `data_factory_models` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='模型配置表';

-- 数据生成任务表
DROP TABLE IF EXISTS `data_factory_generation_tasks`;
CREATE TABLE `data_factory_generation_tasks` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(100) NOT NULL COMMENT '任务名称',
  `description` text DEFAULT NULL COMMENT '任务描述',
  `model_id` int(11) NOT NULL COMMENT '数据模型ID',
  `record_count` int(11) NOT NULL COMMENT '生成数据量',
  `export_format` varchar(20) DEFAULT 'json' COMMENT '导出格式：json, csv, excel, sql',
  `export_config` json DEFAULT NULL COMMENT '导出配置（JSON格式）',
  `status` varchar(20) DEFAULT 'pending' COMMENT '任务状态：pending-等待，running-执行中，completed-完成，failed-失败，cancelled-取消',
  `progress` int(11) DEFAULT 0 COMMENT '执行进度（0-100）',
  `execution_time` int(11) DEFAULT NULL COMMENT '执行时间（秒）',
  `result_file_path` varchar(500) DEFAULT NULL COMMENT '结果文件路径',
  `result_file_size` int(11) DEFAULT NULL COMMENT '结果文件大小（字节）',
  `error_message` text DEFAULT NULL COMMENT '错误信息',
  `memory_usage` int(11) DEFAULT NULL COMMENT '内存使用量（MB）',
  `cpu_usage` int(11) DEFAULT NULL COMMENT 'CPU使用率（%）',
  `started_at` datetime DEFAULT NULL COMMENT '开始执行时间',
  `completed_at` datetime DEFAULT NULL COMMENT '完成时间',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建者',
  `updated_by` varchar(50) DEFAULT NULL COMMENT '更新者',
  PRIMARY KEY (`id`),
  KEY `ix_data_factory_generation_tasks_id` (`id`),
  KEY `ix_data_factory_generation_tasks_model_id` (`model_id`),
  KEY `ix_data_factory_generation_tasks_status` (`status`),
  CONSTRAINT `fk_data_factory_generation_tasks_model_id` FOREIGN KEY (`model_id`) REFERENCES `data_factory_models` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据生成任务表';

-- =====================================================
-- 4. 混沌工程模块
-- =====================================================

-- 混沌任务表
DROP TABLE IF EXISTS `chaos_tasks`;
CREATE TABLE `chaos_tasks` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(100) NOT NULL COMMENT '任务名称',
  `description` text DEFAULT NULL COMMENT '任务描述',
  `task_type` varchar(50) NOT NULL COMMENT '任务类型',
  `target_config` json NOT NULL COMMENT '目标配置（JSON格式）',
  `chaos_config` json NOT NULL COMMENT '混沌配置（JSON格式）',
  `schedule_config` json DEFAULT NULL COMMENT '调度配置（JSON格式）',
  `status` varchar(20) DEFAULT 'draft' COMMENT '任务状态：draft-草稿，active-激活，paused-暂停，completed-完成',
  `execution_count` int(11) DEFAULT 0 COMMENT '执行次数',
  `last_execution` datetime DEFAULT NULL COMMENT '最后执行时间',
  `next_execution` datetime DEFAULT NULL COMMENT '下次执行时间',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建者',
  `updated_by` varchar(50) DEFAULT NULL COMMENT '更新者',
  PRIMARY KEY (`id`),
  KEY `ix_chaos_tasks_id` (`id`),
  KEY `ix_chaos_tasks_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='混沌任务表';

-- 混沌执行记录表
DROP TABLE IF EXISTS `chaos_executions`;
CREATE TABLE `chaos_executions` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `task_id` int(11) NOT NULL COMMENT '任务ID',
  `execution_id` varchar(100) NOT NULL COMMENT '执行ID',
  `status` varchar(20) DEFAULT 'running' COMMENT '执行状态：running-执行中，success-成功，failed-失败，cancelled-取消',
  `start_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `duration` int(11) DEFAULT NULL COMMENT '执行时长（秒）',
  `result` json DEFAULT NULL COMMENT '执行结果（JSON格式）',
  `error_message` text DEFAULT NULL COMMENT '错误信息',
  `logs` longtext DEFAULT NULL COMMENT '执行日志',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `ix_chaos_executions_execution_id` (`execution_id`),
  KEY `ix_chaos_executions_id` (`id`),
  KEY `ix_chaos_executions_task_id` (`task_id`),
  CONSTRAINT `fk_chaos_executions_task_id` FOREIGN KEY (`task_id`) REFERENCES `chaos_tasks` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='混沌执行记录表';

-- =====================================================
-- 5. 初始化数据
-- =====================================================

-- 插入默认角色
INSERT INTO `roles` (`name`, `code`, `description`, `created_by`) VALUES
('超级管理员', 'R_ADMIN', '系统超级管理员，拥有所有权限', 'system'),
('普通用户', 'R_USER', '普通用户，拥有基本功能权限', 'system'),
('测试用户', 'R_TESTER', '测试用户，拥有测试相关权限', 'system');

-- 插入默认用户（密码都是：123456）
INSERT INTO `users` (`username`, `email`, `hashed_password`, `nickname`, `is_superuser`, `created_by`) VALUES
('admin', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3L3jzjvG4W', '系统管理员', 1, 'system'),
('testuser', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3L3jzjvG4W', '测试用户', 0, 'system'),
('developer', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3L3jzjvG4W', '开发者', 0, 'system');

-- 分配用户角色
INSERT INTO `user_roles` (`user_id`, `role_id`) VALUES
(1, 1), -- admin -> R_ADMIN
(2, 2), -- testuser -> R_USER
(2, 3), -- testuser -> R_TESTER
(3, 2); -- developer -> R_USER

-- 插入默认环境
INSERT INTO `environments` (`name`, `description`, `env_type`, `base_url`, `config`, `created_by`) VALUES
('开发环境', '本地开发环境', 'dev', 'http://localhost:8080', '{"timeout": 30, "retries": 3}', 'admin'),
('测试环境', '功能测试环境', 'test', 'http://test.dptest.com', '{"timeout": 60, "retries": 5}', 'admin'),
('预发布环境', '预发布测试环境', 'staging', 'http://staging.dptest.com', '{"timeout": 60, "retries": 3}', 'admin'),
('生产环境', '正式生产环境', 'prod', 'http://prod.dptest.com', '{"timeout": 120, "retries": 1}', 'admin');

-- 插入数据模型示例
INSERT INTO `data_factory_models` (`name`, `description`, `version`, `category`, `fields_config`, `usage_count`, `created_by`) VALUES
('用户信息模型', '用于生成用户基本信息的数据模型', '1.0.0', '用户数据',
'[
  {"name": "id", "type": "integer", "description": "用户ID", "constraints": {"min": 1, "max": 999999}},
  {"name": "username", "type": "string", "description": "用户名", "constraints": {"min_length": 3, "max_length": 20, "pattern": "^[a-zA-Z0-9_]+$"}},
  {"name": "email", "type": "email", "description": "邮箱地址", "constraints": {}},
  {"name": "phone", "type": "phone", "description": "手机号码", "constraints": {"country": "CN"}},
  {"name": "age", "type": "integer", "description": "年龄", "constraints": {"min": 18, "max": 80}},
  {"name": "gender", "type": "enum", "description": "性别", "constraints": {"values": ["male", "female", "other"]}},
  {"name": "created_at", "type": "datetime", "description": "创建时间", "constraints": {}}
]', 15, 'admin'),

('商品信息模型', '用于生成电商商品信息的数据模型', '1.2.0', '电商数据',
'[
  {"name": "product_id", "type": "string", "description": "商品ID", "constraints": {"pattern": "^PRD[0-9]{6}$"}},
  {"name": "name", "type": "string", "description": "商品名称", "constraints": {"min_length": 5, "max_length": 100}},
  {"name": "category", "type": "enum", "description": "商品分类", "constraints": {"values": ["electronics", "clothing", "books", "home", "sports"]}},
  {"name": "price", "type": "decimal", "description": "商品价格", "constraints": {"min": 0.01, "max": 99999.99, "precision": 2}},
  {"name": "stock", "type": "integer", "description": "库存数量", "constraints": {"min": 0, "max": 10000}},
  {"name": "description", "type": "text", "description": "商品描述", "constraints": {"min_length": 10, "max_length": 500}},
  {"name": "is_active", "type": "boolean", "description": "是否上架", "constraints": {}}
]', 8, 'admin'),

('订单信息模型', '用于生成订单交易数据的模型', '1.0.0', '交易数据',
'[
  {"name": "order_id", "type": "string", "description": "订单ID", "constraints": {"pattern": "^ORD[0-9]{8}$"}},
  {"name": "user_id", "type": "integer", "description": "用户ID", "constraints": {"min": 1, "max": 999999}},
  {"name": "product_ids", "type": "array", "description": "商品ID列表", "constraints": {"item_type": "string", "min_items": 1, "max_items": 10}},
  {"name": "total_amount", "type": "decimal", "description": "订单总金额", "constraints": {"min": 0.01, "max": 99999.99, "precision": 2}},
  {"name": "status", "type": "enum", "description": "订单状态", "constraints": {"values": ["pending", "paid", "shipped", "delivered", "cancelled"]}},
  {"name": "order_date", "type": "datetime", "description": "下单时间", "constraints": {}},
  {"name": "shipping_address", "type": "address", "description": "收货地址", "constraints": {"country": "CN"}}
]', 12, 'admin'),

('日志数据模型', '用于生成系统日志数据的模型', '1.1.0', '系统数据',
'[
  {"name": "log_id", "type": "uuid", "description": "日志ID", "constraints": {}},
  {"name": "timestamp", "type": "datetime", "description": "时间戳", "constraints": {}},
  {"name": "level", "type": "enum", "description": "日志级别", "constraints": {"values": ["DEBUG", "INFO", "WARN", "ERROR", "FATAL"]}},
  {"name": "service", "type": "string", "description": "服务名称", "constraints": {"min_length": 3, "max_length": 50}},
  {"name": "message", "type": "text", "description": "日志消息", "constraints": {"min_length": 10, "max_length": 1000}},
  {"name": "user_id", "type": "integer", "description": "用户ID", "constraints": {"min": 1, "max": 999999, "nullable": true}},
  {"name": "ip_address", "type": "ip", "description": "IP地址", "constraints": {"version": "v4"}}
]', 25, 'admin'),

('测试数据模型', '用于API测试的简单数据模型', '1.0.0', '测试数据',
'[
  {"name": "id", "type": "integer", "description": "ID", "constraints": {"min": 1, "max": 1000}},
  {"name": "name", "type": "string", "description": "名称", "constraints": {"min_length": 2, "max_length": 50}},
  {"name": "value", "type": "decimal", "description": "数值", "constraints": {"min": 0, "max": 100, "precision": 2}},
  {"name": "flag", "type": "boolean", "description": "标志", "constraints": {}},
  {"name": "created_at", "type": "datetime", "description": "创建时间", "constraints": {}}
]', 3, 'testuser');

-- 插入模型配置示例
INSERT INTO `model_configs` (`model_id`, `config_name`, `config_type`, `config_data`, `is_default`, `description`, `created_by`) VALUES
(1, '默认生成配置', 'generation',
'{"batch_size": 1000, "parallel_workers": 4, "memory_limit": "512MB", "timeout": 300}',
1, '用户信息模型的默认生成配置', 'admin'),

(1, '高性能生成配置', 'generation',
'{"batch_size": 5000, "parallel_workers": 8, "memory_limit": "1GB", "timeout": 600}',
0, '用户信息模型的高性能生成配置', 'admin'),

(1, '数据验证配置', 'validation',
'{"enable_unique_check": true, "enable_format_check": true, "enable_constraint_check": true, "error_threshold": 0.01}',
1, '用户信息模型的数据验证配置', 'admin'),

(2, '默认生成配置', 'generation',
'{"batch_size": 500, "parallel_workers": 2, "memory_limit": "256MB", "timeout": 180}',
1, '商品信息模型的默认生成配置', 'admin'),

(2, '导出配置', 'export',
'{"formats": ["json", "csv", "excel"], "compression": "gzip", "encoding": "utf-8", "include_headers": true}',
1, '商品信息模型的导出配置', 'admin'),

(3, '默认生成配置', 'generation',
'{"batch_size": 2000, "parallel_workers": 6, "memory_limit": "768MB", "timeout": 450}',
1, '订单信息模型的默认生成配置', 'admin'),

(3, '关联数据配置', 'generation',
'{"enable_foreign_key": true, "user_id_range": [1, 10000], "product_id_pool": 500, "order_date_range": ["2024-01-01", "2025-01-30"]}',
0, '订单信息模型的关联数据生成配置', 'admin'),

(4, '日志生成配置', 'generation',
'{"batch_size": 10000, "parallel_workers": 8, "memory_limit": "1GB", "timeout": 900, "time_distribution": "normal"}',
1, '日志数据模型的生成配置', 'admin'),

(4, '日志验证配置', 'validation',
'{"enable_timestamp_check": true, "enable_level_check": true, "enable_message_format": true, "max_message_length": 1000}',
1, '日志数据模型的验证配置', 'admin'),

(5, '测试配置', 'generation',
'{"batch_size": 100, "parallel_workers": 1, "memory_limit": "128MB", "timeout": 60}',
1, '测试数据模型的简单配置', 'testuser');

-- 插入数据生成任务示例
INSERT INTO `data_factory_generation_tasks` (`name`, `description`, `model_id`, `record_count`, `export_format`, `status`, `progress`, `execution_time`, `created_by`) VALUES
('用户数据生成任务', '生成1000条用户测试数据', 1, 1000, 'json', 'completed', 100, 45, 'admin'),
('商品数据生成任务', '生成500条商品数据用于测试', 2, 500, 'csv', 'completed', 100, 32, 'admin'),
('订单数据生成任务', '生成2000条订单数据', 3, 2000, 'excel', 'running', 65, NULL, 'testuser'),
('日志数据生成任务', '生成10000条系统日志', 4, 10000, 'json', 'pending', 0, NULL, 'admin'),
('测试数据批量生成', '生成100条测试数据', 5, 100, 'csv', 'failed', 25, NULL, 'testuser');

-- 插入混沌任务示例
INSERT INTO `chaos_tasks` (`name`, `description`, `task_type`, `target_config`, `chaos_config`, `schedule_config`, `status`, `execution_count`, `created_by`) VALUES
('API延迟注入测试', '对用户API注入网络延迟', 'network_delay',
'{"service": "user-api", "endpoints": ["/api/users", "/api/users/{id}"], "environment": "test"}',
'{"delay_ms": 2000, "probability": 0.5, "duration": 300}',
'{"type": "manual", "enabled": false}', 'draft', 0, 'admin'),

('数据库连接故障模拟', '模拟数据库连接中断', 'database_failure',
'{"database": "mysql", "host": "test-db.dptest.com", "port": 3306}',
'{"failure_type": "connection_timeout", "duration": 60, "probability": 1.0}',
'{"type": "cron", "expression": "0 2 * * *", "enabled": false}', 'active', 3, 'admin'),

('服务器CPU压力测试', '对目标服务器施加CPU压力', 'cpu_stress',
'{"servers": ["test-server-1", "test-server-2"], "environment": "test"}',
'{"cpu_percent": 80, "duration": 180, "cores": 2}',
'{"type": "interval", "interval_minutes": 60, "enabled": true}', 'active', 12, 'testuser'),

('内存泄漏模拟', '模拟应用内存泄漏场景', 'memory_leak',
'{"service": "order-service", "environment": "staging"}',
'{"leak_rate_mb": 10, "max_memory_mb": 512, "duration": 600}',
'{"type": "manual", "enabled": false}', 'paused', 1, 'admin');

-- 插入混沌执行记录示例
INSERT INTO `chaos_executions` (`task_id`, `execution_id`, `status`, `start_time`, `end_time`, `duration`, `result`) VALUES
(2, 'exec_20250130_001', 'success', '2025-01-30 02:00:00', '2025-01-30 02:01:00', 60,
'{"affected_connections": 15, "error_rate": 0.95, "recovery_time": 45}'),
(2, 'exec_20250129_001', 'success', '2025-01-29 02:00:00', '2025-01-29 02:01:00', 60,
'{"affected_connections": 12, "error_rate": 0.88, "recovery_time": 52}'),
(3, 'exec_20250130_003', 'running', '2025-01-30 10:30:00', NULL, NULL, NULL),
(3, 'exec_20250130_002', 'success', '2025-01-30 09:30:00', '2025-01-30 09:33:00', 180,
'{"cpu_usage_peak": 82, "response_time_impact": 1.5, "error_count": 3}');

-- 恢复外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- =====================================================
-- 脚本执行完成
-- 数据库初始化完毕，包含：
-- - 完整的表结构定义
-- - 默认用户和角色数据
-- - 环境配置数据
-- - 数据工厂测试数据
-- - 混沌工程测试数据
-- =====================================================

"""
混沌测试批次任务业务服务
"""
import asyncio
import time
from typing import Dict, Any, List, Optional
from datetime import datetime

from sqlalchemy.ext.asyncio import AsyncSession

from app.services.base import BaseService
from app.repositories.chaos.chaos_batch_task_repository import ChaosBatchTaskRepository, ChaosBatchTaskItemRepository
from app.repositories.chaos.chaos_execution_repository import ChaosExecutionRepository
from app.repositories.env.env import EnvironmentRepository
from app.repositories.user.user import UserRepository
from app.models.chaos.chaos_batch_task import ChaosBatchTask, ChaosBatchTaskItem
from app.models.chaos.chaos_execution import ChaosExecution
from app.schemas.chaos.chaos_batch_task import (
    ChaosBatchTask<PERSON>reate, ChaosBatchTaskUpdate, ChaosBatchTaskResponse,
    ChaosBatchTaskItemResponse, ChaosBatchTaskExecuteRequest,
    ChaosBatchTaskQuery, ChaosBatchTaskPageResponse, ChaosBatchTaskStatistics
)
from app.core.exceptions import raise_validation_error, raise_not_found
from app.core.error_codes import ErrorCode
from app.utils.logger import setup_logger, operation_logger
from .chaosblade_service import ChaosBladeService

logger = setup_logger()


class ChaosBatchTaskService(BaseService[ChaosBatchTask, ChaosBatchTaskCreate, ChaosBatchTaskUpdate, ChaosBatchTaskResponse]):
    """
    批次任务业务服务
    处理批次任务的创建、执行、状态管理等业务逻辑
    """

    def __init__(self, db: AsyncSession):
        repository = ChaosBatchTaskRepository(db)
        super().__init__(db, repository)
        self.item_repository = ChaosBatchTaskItemRepository(db)
        self.execution_repository = ChaosExecutionRepository(db)
        self.env_repository = EnvironmentRepository(db)
        self.user_repository = UserRepository(db)
        self.chaosblade_service = ChaosBladeService()

    # ==================== 抽象属性实现 ====================

    @property
    def model_class(self):
        """返回模型类"""
        return ChaosBatchTask

    @property
    def response_schema_class(self):
        """返回响应Schema类"""
        return ChaosBatchTaskResponse

    # ==================== 钩子方法实现 ====================

    async def _validate_before_create(self, create_data: ChaosBatchTaskCreate, **kwargs) -> None:
        """创建前业务验证"""
        # 验证环境是否存在
        env = await self.env_repository.get(create_data.env_id)
        if not env:
            raise_validation_error(ErrorCode.ENV_NOT_FOUND, f"环境ID {create_data.env_id} 不存在")

        # 验证任务名称是否重复
        existing_tasks = await self.repository.search_tasks_with_query(
            ChaosBatchTaskQuery(keyword=create_data.name, size=1)
        )
        if existing_tasks[0]:  # 如果有结果
            for task in existing_tasks[0]:
                if task.name == create_data.name:
                    raise_validation_error(ErrorCode.CHAOS_TASK_NAME_INVALID, f"批次任务名称 '{create_data.name}' 已存在")

        # 验证子任务配置
        if not create_data.task_items:
            raise_validation_error(ErrorCode.FIELD_VALUE_INVALID, "批次任务必须包含至少一个子任务")

        # 验证故障类型
        valid_fault_types = ['cpu', 'memory', 'network', 'disk', 'process', 'k8s']
        for item in create_data.task_items:
            if item.fault_type not in valid_fault_types:
                raise_validation_error(ErrorCode.CHAOS_PARAMS_INVALID, f"不支持的故障类型: {item.fault_type}")

    async def _process_before_create(self, create_dict: Dict[str, Any]) -> Dict[str, Any]:
        """创建前数据处理"""
        # 设置默认状态
        create_dict["status"] = "pending"
        return create_dict

    async def _process_after_create(self, obj: ChaosBatchTask, create_data) -> None:
        """创建后处理"""
        logger.info(f"批次任务创建成功: {obj.name} (ID: {obj.id})")
        
        # 如果是定时任务，创建调度配置
        if obj.execution_type in ["scheduled", "periodic", "cron"]:
            await self._create_schedule_task_config(obj, int(obj.created_by))

    async def _validate_before_update(self, obj: ChaosBatchTask, update_data: ChaosBatchTaskUpdate, **kwargs) -> None:
        """更新前业务验证"""
        # 检查任务是否可以编辑
        if not obj.can_edit:
            raise_validation_error(ErrorCode.OPERATION_NOT_ALLOWED, f"任务状态为 {obj.status}，无法编辑")

        # 如果更新环境ID，验证环境是否存在
        if update_data.env_id and update_data.env_id != obj.env_id:
            env = await self.env_repository.get(update_data.env_id)
            if not env:
                raise_validation_error(ErrorCode.ENV_NOT_FOUND, f"环境ID {update_data.env_id} 不存在")

    async def _process_after_update(self, obj: ChaosBatchTask, update_data, updated_by: int) -> None:
        """更新后处理"""
        logger.info(f"批次任务更新成功: {obj.name} (ID: {obj.id})")
        
        # 如果执行配置发生变化，更新调度配置
        if obj.execution_type in ["scheduled", "periodic", "cron"]:
            await self._update_schedule_task_config(obj, updated_by)

    async def _convert_to_response(self, obj: ChaosBatchTask) -> ChaosBatchTaskResponse:
        """将批次任务对象转换为响应模型（包含真实环境名称和用户昵称）"""
        # 获取环境名称
        env_name = None
        try:
            if obj.env_id:
                env = await self.env_repository.get(obj.env_id)
                if env:
                    env_name = env.name
                else:
                    env_name = f"环境{obj.env_id}"
        except Exception:
            env_name = f"环境{obj.env_id}" if obj.env_id else None

        # 获取创建者和更新者昵称
        created_by_name = obj.created_by
        updated_by_name = obj.updated_by

        try:
            if obj.created_by and obj.created_by.isdigit():
                creator = await self.user_repository.get(int(obj.created_by))
                if creator:
                    created_by_name = creator.nickname or creator.username
        except Exception:
            pass

        try:
            if obj.updated_by and obj.updated_by.isdigit():
                updater = await self.user_repository.get(int(obj.updated_by))
                if updater:
                    updated_by_name = updater.nickname or updater.username
        except Exception:
            pass

        # 转换子任务
        task_items = []
        if hasattr(obj, 'task_items') and obj.task_items:
            for item in obj.task_items:
                item_response = ChaosBatchTaskItemResponse(
                    id=item.id,
                    batch_task_id=item.batch_task_id,
                    name=item.name,
                    description=item.description,
                    fault_type=item.fault_type,
                    fault_params=item.fault_params,
                    task_order=item.task_order,
                    auto_destroy=item.auto_destroy,
                    max_duration=item.max_duration,
                    created_at=item.created_at.strftime("%Y-%m-%d %H:%M:%S") if item.created_at else None,
                    updated_at=item.updated_at.strftime("%Y-%m-%d %H:%M:%S") if item.updated_at else None,
                    created_by=item.created_by,
                    updated_by=item.updated_by
                )
                task_items.append(item_response)

        return ChaosBatchTaskResponse(
            id=obj.id,
            name=obj.name,
            description=obj.description,
            env_id=obj.env_id,
            batch_execution_mode=obj.batch_execution_mode,
            execution_type=obj.execution_type,
            scheduled_time=obj.scheduled_time,
            periodic_config=obj.periodic_config,
            cron_expression=obj.cron_expression,
            status=obj.status,
            task_status=obj.task_status,
            execution_result=obj.execution_result,
            auto_destroy=obj.auto_destroy,
            max_duration=obj.max_duration,
            environment_name=env_name,
            task_count=obj.task_count,
            task_items=task_items,
            is_running=obj.is_running,
            is_completed=obj.is_completed,
            can_execute=obj.can_execute,
            can_stop=obj.can_stop,
            can_edit=obj.can_edit,
            can_delete=obj.can_delete,
            created_at=obj.created_at.strftime("%Y-%m-%d %H:%M:%S") if obj.created_at else None,
            updated_at=obj.updated_at.strftime("%Y-%m-%d %H:%M:%S") if obj.updated_at else None,
            created_by=created_by_name,
            updated_by=updated_by_name
        )

    async def _batch_convert_to_response(self, tasks: List[ChaosBatchTask]) -> List[ChaosBatchTaskResponse]:
        """批量转换任务对象为响应模型（优化性能，避免N+1查询）"""
        if not tasks:
            return []

        # 使用基类的通用方法获取用户信息映射
        user_map = await self._get_user_info_map(tasks)

        # 收集所有需要查询的环境ID
        env_ids = set()
        for task in tasks:
            if task.env_id:
                env_ids.add(task.env_id)

        # 批量查询环境信息
        env_map = {}
        try:
            if env_ids:
                envs = await self.env_repository.get_by_ids(list(env_ids))
                env_map = {env.id: env.name for env in envs}
        except Exception:
            pass

        # 转换所有任务
        task_responses = []
        for task in tasks:
            # 获取环境名称
            env_name = env_map.get(task.env_id, f"环境{task.env_id}" if task.env_id else None)

            # 使用基类通用方法获取用户名称
            created_by_name = self._get_user_display_name(task.created_by, user_map)
            updated_by_name = self._get_user_display_name(task.updated_by, user_map)

            # 转换子任务（列表页面不需要详细的子任务信息，只需要数量）
            task_items = []
            if hasattr(task, 'task_items') and task.task_items:
                for item in task.task_items:
                    item_response = ChaosBatchTaskItemResponse(
                        id=item.id,
                        batch_task_id=item.batch_task_id,
                        name=item.name,
                        description=item.description,
                        fault_type=item.fault_type,
                        fault_params=item.fault_params,
                        task_order=item.task_order,
                        auto_destroy=item.auto_destroy,
                        max_duration=item.max_duration,
                        created_at=item.created_at.strftime("%Y-%m-%d %H:%M:%S") if item.created_at else None,
                        updated_at=item.updated_at.strftime("%Y-%m-%d %H:%M:%S") if item.updated_at else None,
                        created_by=item.created_by,
                        updated_by=item.updated_by
                    )
                    task_items.append(item_response)

            task_response = ChaosBatchTaskResponse(
                id=task.id,
                name=task.name,
                description=task.description,
                env_id=task.env_id,
                batch_execution_mode=task.batch_execution_mode,
                wait_time=getattr(task, 'wait_time', 30),  # 安全获取wait_time字段
                execution_type=task.execution_type,
                scheduled_time=task.scheduled_time,
                periodic_config=task.periodic_config,
                cron_expression=task.cron_expression,
                status=task.status,
                task_status=task.task_status,
                execution_result=task.execution_result,
                auto_destroy=task.auto_destroy,
                max_duration=task.max_duration,
                environment_name=env_name,
                task_count=task.task_count,
                task_items=task_items,
                is_running=task.is_running,
                is_completed=task.is_completed,
                can_execute=task.can_execute,
                can_stop=task.can_stop,
                can_edit=task.can_edit,
                can_delete=task.can_delete,
                created_at=task.created_at.strftime("%Y-%m-%d %H:%M:%S") if task.created_at else None,
                updated_at=task.updated_at.strftime("%Y-%m-%d %H:%M:%S") if task.updated_at else None,
                created_by=created_by_name,
                updated_by=updated_by_name
            )
            task_responses.append(task_response)

        return task_responses

    # ==================== 业务方法 ====================

    async def create_batch_task(self, task_data: ChaosBatchTaskCreate, current_user_id: int) -> ChaosBatchTaskResponse:
        """创建批次任务"""
        # 使用Repository的create_with_items方法
        task = await self.repository.create_with_items(task_data, current_user_id)
        return await self._convert_to_response(task)

    async def update_batch_task(
        self, 
        task_id: int, 
        task_data: ChaosBatchTaskUpdate, 
        current_user_id: int
    ) -> ChaosBatchTaskResponse:
        """更新批次任务"""
        # 先获取任务进行验证
        task = await self.repository.get_with_items(task_id)
        if not task:
            raise_not_found(f"批次任务不存在: {task_id}")

        # 执行验证
        await self._validate_before_update(task, task_data)

        # 使用Repository的update_with_items方法
        updated_task = await self.repository.update_with_items(task_id, task_data, current_user_id)
        if not updated_task:
            raise_not_found(f"批次任务不存在: {task_id}")

        # 执行更新后处理
        await self._process_after_update(updated_task, task_data, current_user_id)

        return await self._convert_to_response(updated_task)

    async def get_batch_task_detail(self, task_id: int) -> ChaosBatchTaskResponse:
        """获取批次任务详情"""
        task = await self.repository.get_with_items(task_id)
        if not task:
            raise_not_found(f"批次任务不存在: {task_id}")
        
        return await self._convert_to_response(task)

    async def list_batch_tasks(self, query: ChaosBatchTaskQuery) -> ChaosBatchTaskPageResponse:
        """查询批次任务列表"""
        tasks, total = await self.repository.search_tasks_with_query(query)

        # 批量转换为响应格式（优化性能）
        task_responses = await self._batch_convert_to_response(tasks)

        # 计算总页数
        pages = (total + query.size - 1) // query.size

        return ChaosBatchTaskPageResponse(
            items=task_responses,
            total=total,
            page=query.page,
            size=query.size,
            pages=pages
        )

    async def delete_batch_task(self, task_id: int, current_user_id: int) -> bool:
        """删除批次任务"""
        task = await self.repository.get(task_id)
        if not task:
            raise_not_found(f"批次任务不存在: {task_id}")

        # 检查是否可以删除
        if not task.can_delete:
            raise_validation_error(ErrorCode.OPERATION_NOT_ALLOWED, f"任务状态为 {task.status}，无法删除")

        # 删除任务（包括子任务）
        success = await self.repository.delete_with_items(task_id)
        if success:
            logger.info(f"用户 {current_user_id} 删除了批次任务: {task.name} (ID: {task_id})")
        
        return success

    async def get_batch_task_statistics(self) -> ChaosBatchTaskStatistics:
        """获取批次任务统计信息"""
        stats = await self.repository.get_statistics()
        
        return ChaosBatchTaskStatistics(
            total_count=stats["total_count"],
            status_stats=stats["status_stats"],
            execution_mode_stats=stats["execution_mode_stats"],
            env_stats=stats["env_stats"]
        )

    # ==================== 调度相关方法 ====================

    async def _create_schedule_task_config(self, obj: ChaosBatchTask, created_by: int):
        """创建定时任务配置"""
        try:
            from app.services.schedule.schedule_task_service import ScheduleTaskService

            schedule_service = ScheduleTaskService(self.db)
            task_id = await schedule_service.create_chaos_batch_schedule_task(
                batch_task_id=obj.id,
                batch_task_name=obj.name,
                execution_type=obj.execution_type,
                created_by=created_by,
                scheduled_time=obj.scheduled_time,
                cron_expression=obj.cron_expression,
                periodic_config=obj.periodic_config
            )

            logger.info(f"✅ 批次任务定时配置创建成功: {task_id}")

        except Exception as e:
            logger.error(f"❌ 创建批次任务定时配置失败: {obj.id}, 错误: {str(e)}")

    async def _update_schedule_task_config(self, obj: ChaosBatchTask, updated_by: int):
        """更新定时任务配置，如果不存在则创建"""
        try:
            from app.services.schedule.schedule_task_service import ScheduleTaskService

            schedule_service = ScheduleTaskService(self.db)

            # 先尝试更新现有配置
            success = await schedule_service.update_chaos_batch_schedule_task(
                batch_task_id=obj.id,
                batch_task_name=obj.name,
                execution_type=obj.execution_type,
                updated_by=updated_by,
                scheduled_time=obj.scheduled_time,
                cron_expression=obj.cron_expression,
                periodic_config=obj.periodic_config
            )

            if success:
                logger.info(f"✅ 批次任务定时配置更新成功: chaos_batch_task_{obj.id}")
            else:
                # 如果更新失败，说明配置不存在，创建新的配置
                logger.info(f"📝 批次任务定时配置不存在，创建新配置: chaos_batch_task_{obj.id}")
                task_id = await schedule_service.create_chaos_batch_schedule_task(
                    batch_task_id=obj.id,
                    batch_task_name=obj.name,
                    execution_type=obj.execution_type,
                    created_by=updated_by,
                    scheduled_time=obj.scheduled_time,
                    cron_expression=obj.cron_expression,
                    periodic_config=obj.periodic_config
                )
                logger.info(f"✅ 批次任务定时配置创建成功: {task_id}")

        except Exception as e:
            logger.error(f"❌ 更新批次任务定时配置失败: {obj.id}, 错误: {str(e)}")

    # ==================== 执行相关方法 ====================

    async def execute_batch_task(
        self,
        task_id: int,
        request: ChaosBatchTaskExecuteRequest,
        current_user_id: int
    ) -> Dict[str, Any]:
        """执行批次任务"""
        start_time = time.time()

        # 获取任务详情
        task = await self.repository.get_with_items(task_id)
        if not task:
            raise_not_found(f"批次任务不存在: {task_id}")

        # 检查任务是否可以执行
        if not request.force and not task.can_execute:
            raise_validation_error(ErrorCode.CHAOS_TASK_CANNOT_EXECUTE, f"任务状态为 {task.status}，无法执行")

        # 检查子任务
        if not task.task_items:
            raise_validation_error(ErrorCode.FIELD_VALUE_INVALID, "批次任务没有子任务，无法执行")

        # 获取环境信息
        env = await self.env_repository.get(task.env_id)
        if not env:
            raise_validation_error(ErrorCode.ENV_NOT_FOUND, f"环境不存在: {task.env_id}")

        # 构建主机列表（与单次任务逻辑保持一致）
        hosts = [{
            "id": env.id,
            "name": env.name,
            "host": env.host,
            "port": env.port or 22,
            "username": env.config.get("username", "root") if env.config else "root",
            "password": env.config.get("password") if env.config else None,
            "private_key_path": env.config.get("private_key_path") if env.config else None
        }]

        try:
            # 更新任务状态为运行中
            task.update_status("running", {
                "start_time": datetime.now().isoformat(),
                "execution_mode": task.batch_execution_mode,
                "total_items": len(task.task_items),
                "total_hosts": len(hosts)
            })
            await self.db.commit()

            logger.info(f"开始执行批次任务: {task.name} (ID: {task_id})")

            # 根据执行模式执行任务
            if task.batch_execution_mode == "sequential":
                # 间隔执行：一个子任务完成后等待一段时间再执行下一个
                result = await self._execute_sequential(task, hosts, request)
            else:  # order
                # 连续执行：按顺序连续执行所有子任务
                result = await self._execute_order(task, hosts, request)

            # 计算执行时间
            execution_time = time.time() - start_time

            # 根据执行结果更新任务状态
            if result["success"]:
                if task.batch_execution_mode == "sequential":
                    # 间隔执行：所有任务都已经完成（包括等待间隔），直接设为completed
                    final_status = "completed"
                    logger.info(f"批次任务 {task_id} 间隔执行成功，所有任务已完成，状态设为completed")
                else:  # order 连续执行
                    # 连续执行：检查是否有timeout参数，决定任务状态
                    max_timeout = 0
                    for item in task.task_items:
                        if item.fault_params and item.fault_params.get("timeout"):
                            try:
                                timeout_seconds = int(item.fault_params.get("timeout", 0))
                                max_timeout = max(max_timeout, timeout_seconds)
                            except (ValueError, TypeError):
                                pass

                    if max_timeout > 0:
                        # 连续执行有timeout，需要等待所有任务完成
                        final_status = "running"
                        logger.info(f"批次任务 {task_id} 连续执行成功，最大timeout参数({max_timeout}秒)，状态设为running")

                        # 启动后台任务监控timeout完成
                        await self._schedule_batch_task_completion(task_id, max_timeout)
                    else:
                        # 连续执行无timeout，直接完成
                        final_status = "completed"
                        logger.info(f"批次任务 {task_id} 连续执行成功，无timeout参数，状态设为completed")
            else:
                # 执行失败
                final_status = "failed"

            # 更新任务状态
            status_data = {
                **result,
                "execution_time_seconds": round(execution_time, 2)
            }

            # 只有在任务完成时才设置结束时间
            if final_status in ["completed", "failed"]:
                status_data["end_time"] = datetime.now().isoformat()

            task.update_status(final_status, status_data)
            await self.db.commit()

            logger.info(f"批次任务执行完成: {task.name}, 状态: {final_status}, 耗时: {execution_time:.2f}秒")

            return {
                "success": result["success"],
                "task_id": task_id,
                "task_name": task.name,
                "execution_mode": task.batch_execution_mode,
                "total_items": len(task.task_items),
                "successful_items": result.get("successful_items", 0),
                "failed_items": result.get("failed_items", 0),
                "execution_time": round(execution_time, 2),
                "details": result.get("details", [])
            }

        except Exception as e:
            # 执行失败，更新任务状态
            error_msg = str(e)
            task.update_status("failed", {
                "error": error_msg,
                "end_time": datetime.now().isoformat(),
                "execution_time_seconds": round(time.time() - start_time, 2)
            })
            await self.db.commit()

            logger.error(f"批次任务执行失败: {task.name}, 错误: {error_msg}")
            raise

    async def _execute_sequential(
        self,
        task: ChaosBatchTask,
        hosts: List[Dict],
        request: ChaosBatchTaskExecuteRequest
    ) -> Dict[str, Any]:
        """间隔执行：一个子任务完成后等待一段时间再执行下一个（真正的间隔等待执行）"""
        import asyncio

        successful_items = 0
        failed_items = 0
        details = []

        # 按顺序执行每个子任务
        sorted_items = sorted(task.task_items, key=lambda x: x.task_order)

        for i, item in enumerate(sorted_items):
            try:
                logger.info(f"开始执行子任务: {item.name} (顺序: {item.task_order}, {i+1}/{len(sorted_items)})")

                # 执行单个子任务
                item_result = await self._execute_single_item(task, item, hosts, request)
                details.append(item_result)

                if item_result["success"]:
                    successful_items += 1
                    logger.info(f"子任务执行成功: {item.name}")

                    # 如果不是最后一个任务，需要等待任务完成和间隔时间
                    logger.info(f"DEBUG: 检查是否需要等待间隔，i={i}, len={len(sorted_items)}, 条件={i < len(sorted_items) - 1}")
                    if i < len(sorted_items) - 1:
                        logger.info(f"DEBUG: 开始间隔等待逻辑")

                        # 1. 等待任务的故障注入完成
                        timeout_seconds = 0
                        if item.fault_params and item.fault_params.get("timeout"):
                            try:
                                timeout_seconds = int(item.fault_params.get("timeout", 0))
                            except (ValueError, TypeError):
                                timeout_seconds = 0

                        logger.info(f"DEBUG: timeout_seconds={timeout_seconds}")
                        if timeout_seconds > 0:
                            logger.info(f"等待子任务 {item.name} 的故障注入完成，预计时间: {timeout_seconds}秒")
                            await self._wait_for_task_completion(item, timeout_seconds)
                            logger.info(f"子任务 {item.name} 的故障注入已完成")

                        # 2. 额外等待间隔时间
                        wait_time = getattr(task, 'wait_time', 30) or 30  # 默认30秒间隔
                        logger.info(f"DEBUG: wait_time={wait_time}")
                        logger.info(f"等待间隔时间: {wait_time}秒")
                        await asyncio.sleep(wait_time)
                        logger.info(f"间隔等待完成，继续执行下一个任务")
                    else:
                        logger.info(f"DEBUG: 这是最后一个任务，不需要间隔等待")

                else:
                    failed_items += 1
                    logger.warning(f"子任务执行失败: {item.name}, 错误: {item_result.get('error')}")

                    # 如果配置了失败停止，则中断执行
                    # 这里可以根据需要添加停止逻辑

            except Exception as e:
                failed_items += 1
                error_msg = f"子任务 {item.name} 执行异常: {str(e)}"
                logger.error(error_msg)
                details.append({
                    "item_id": item.id,
                    "item_name": item.name,
                    "success": False,
                    "error": error_msg
                })

        return {
            "success": failed_items == 0,
            "successful_items": successful_items,
            "failed_items": failed_items,
            "details": details
        }

    async def _execute_order(
        self,
        task: ChaosBatchTask,
        hosts: List[Dict],
        request: ChaosBatchTaskExecuteRequest
    ) -> Dict[str, Any]:
        """连续执行：按顺序连续执行所有子任务"""
        # 目前与sequential模式相同，后续可以根据需要调整
        return await self._execute_sequential(task, hosts, request)

    async def _execute_single_item(
        self,
        task: ChaosBatchTask,
        item: ChaosBatchTaskItem,
        hosts: List[Dict],
        request: ChaosBatchTaskExecuteRequest
    ) -> Dict[str, Any]:
        """执行单个子任务"""
        try:
            # 为每个主机创建执行记录
            execution_results = []

            for host_info in hosts:
                # 创建执行记录
                execution = ChaosExecution(
                    batch_task_id=task.id,
                    batch_task_item_id=item.id,
                    host_id=host_info["id"],
                    host_info=host_info,
                    fault_config={
                        "fault_type": item.fault_type,
                        **item.fault_params
                    },
                    status="pending"
                )

                self.db.add(execution)
                await self.db.flush()  # 获取execution.id

                # 开始执行
                execution.start_execution(command="准备执行故障注入")
                await self.db.commit()

                # 执行故障注入
                result = await self._execute_fault_on_host(item, host_info, execution.id)
                execution_results.append(result)

            # 汇总结果
            successful_hosts = sum(1 for r in execution_results if r["success"])
            failed_hosts = sum(1 for r in execution_results if not r["success"])

            return {
                "item_id": item.id,
                "item_name": item.name,
                "success": failed_hosts == 0,
                "total_hosts": len(hosts),
                "successful_hosts": successful_hosts,
                "failed_hosts": failed_hosts,
                "execution_results": execution_results
            }

        except Exception as e:
            return {
                "item_id": item.id,
                "item_name": item.name,
                "success": False,
                "error": str(e)
            }

    async def _execute_fault_on_host(
        self,
        item: ChaosBatchTaskItem,
        host_info: Dict[str, Any],
        execution_id: int
    ) -> Dict[str, Any]:
        """在指定主机上执行故障注入"""
        try:
            # 构建故障配置
            fault_config = {
                "fault_type": item.fault_type,
                **item.fault_params
            }

            # 执行故障注入
            result = await self.chaosblade_service.execute_fault(host_info, fault_config)

            # 获取执行记录并更新状态
            execution = await self.execution_repository.get(execution_id)
            if execution:
                if result["success"]:
                    execution.status = "running"
                    execution.output = result.get("output")
                    execution.exit_code = result.get("exit_code", 0)
                    if result.get("chaos_uid"):
                        execution.chaos_uid = result["chaos_uid"]
                    if result.get("command"):
                        execution.command = result["command"]
                else:
                    execution.complete_execution(
                        success=False,
                        error=result.get("error"),
                        exit_code=result.get("exit_code", 1)
                    )

                await self.db.commit()

            return result

        except Exception as e:
            logger.error(f"主机故障注入执行失败: {host_info.get('name', 'Unknown')}, 错误: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "host_id": host_info.get("id"),
                "host_name": host_info.get("name", "Unknown")
            }

    async def _schedule_batch_task_completion(self, task_id: int, timeout_seconds: int) -> None:
        """使用asyncio安排批次任务在timeout后自动完成"""
        try:
            logger.info(f"安排批次任务 {task_id} 在 {timeout_seconds} 秒后自动完成")

            # 直接使用asyncio方案，避免调度器序列化问题
            await self._fallback_schedule_batch_task_completion(task_id, timeout_seconds)

        except Exception as e:
            logger.error(f"安排批次任务 {task_id} 自动完成失败: {str(e)}")

    async def _complete_batch_task_by_timeout(self, task_id: int, timeout_seconds: int) -> None:
        """timeout后自动完成批次任务"""
        try:
            # 获取任务
            task = await self.repository.get(task_id)
            if not task:
                logger.warning(f"批次任务 {task_id} 不存在，无法自动完成")
                return

            # 检查任务状态
            if task.status != "running":
                logger.info(f"批次任务 {task_id} 状态为 {task.status}，无需自动完成")
                return

            # 更新任务状态为完成
            task.update_status("completed", {
                "end_time": datetime.now().isoformat(),
                "auto_completed": True,
                "timeout_seconds": timeout_seconds
            })
            await self.db.commit()

            logger.info(f"批次任务 {task_id} 已自动完成（timeout: {timeout_seconds}秒）")

        except Exception as e:
            logger.error(f"自动完成批次任务 {task_id} 失败: {str(e)}")

    async def _fallback_schedule_batch_task_completion(self, task_id: int, timeout_seconds: int) -> None:
        """回退方案：使用asyncio.sleep（仅用于调度器失败时的临时兼容）"""
        import asyncio

        async def complete_task_after_timeout():
            try:
                await asyncio.sleep(timeout_seconds)
                await self._complete_batch_task_by_timeout(task_id, timeout_seconds)
            except Exception as e:
                logger.error(f"回退方案自动完成批次任务 {task_id} 失败: {str(e)}")

        # 启动后台任务
        asyncio.create_task(complete_task_after_timeout())

    async def _wait_for_task_completion(self, item: ChaosBatchTaskItem, timeout_seconds: int) -> None:
        """智能等待任务完成 - 使用简单等待机制"""
        import asyncio

        try:
            # 使用简单的时间等待机制
            # 这是最可靠的方式，避免调度器序列化问题
            logger.info(f"等待子任务 {item.name} 完成，预计时间: {timeout_seconds}秒")
            await asyncio.sleep(timeout_seconds)
            logger.info(f"子任务 {item.name} 等待完成")

        except Exception as e:
            logger.error(f"等待子任务 {item.name} 完成时出错: {str(e)}")
            # 出错时仍然等待预定时间，确保不会过快执行下一个任务
            await asyncio.sleep(timeout_seconds)

    async def stop_batch_task(self, task_id: int, current_user_id: int) -> Dict[str, Any]:
        """停止批次任务执行"""
        task = await self.repository.get_with_executions(task_id)
        if not task:
            raise_not_found(f"批次任务不存在: {task_id}")

        if not task.can_stop:
            raise_validation_error(ErrorCode.CHAOS_TASK_CANNOT_EXECUTE, f"任务状态为 {task.status}，无法停止")

        try:
            # 停止所有正在运行的执行记录
            stopped_count = 0
            if hasattr(task, 'executions') and task.executions:
                for execution in task.executions:
                    if execution.is_running and execution.chaos_uid:
                        # 调用ChaosBlade停止故障
                        try:
                            await self.chaosblade_service.destroy_fault(
                                execution.host_info,
                                execution.chaos_uid
                            )
                            execution.cancel_execution("用户手动停止")
                            stopped_count += 1
                        except Exception as e:
                            logger.error(f"停止执行记录失败: {execution.id}, 错误: {str(e)}")

            # 更新任务状态
            task.update_status("cancelled", {
                "cancelled_by": current_user_id,
                "cancelled_at": datetime.now().isoformat(),
                "stopped_executions": stopped_count
            })
            await self.db.commit()

            logger.info(f"用户 {current_user_id} 停止了批次任务: {task.name} (ID: {task_id})")

            return {
                "success": True,
                "task_id": task_id,
                "task_name": task.name,
                "stopped_executions": stopped_count,
                "message": f"已停止批次任务，共停止 {stopped_count} 个执行记录"
            }

        except Exception as e:
            logger.error(f"停止批次任务失败: {task_id}, 错误: {str(e)}")
            raise

    async def reset_batch_task(self, task_id: int, current_user_id: int) -> bool:
        """重置批次任务状态"""
        # 获取任务
        task = await self.repository.get(task_id)
        if not task:
            raise_not_found(f"批次任务不存在: {task_id}")

        # 检查是否可以重置
        if task.status == "running":
            raise_validation_error(ErrorCode.OPERATION_NOT_ALLOWED, f"任务正在运行中，无法重置")

        try:
            # 重置任务状态
            task.update_status("pending", {
                "reset_time": datetime.now().isoformat(),
                "reset_by": current_user_id
            })
            await self.db.commit()

            logger.info(f"用户 {current_user_id} 重置了批次任务: {task.name} (ID: {task_id})")
            return True

        except Exception as e:
            await self.db.rollback()
            logger.error(f"重置批次任务失败: {task.name} (ID: {task_id}), 错误: {str(e)}")
            raise

    async def enable_batch_task(self, task_id: int, current_user_id: int) -> bool:
        """启用批次任务"""
        # 获取任务
        task = await self.repository.get(task_id)
        if not task:
            raise_not_found(f"批次任务不存在: {task_id}")

        try:
            # 更新任务状态为启用
            update_data = ChaosBatchTaskUpdate(task_status="enabled")
            await self.repository.update_with_items(task_id, update_data, current_user_id)
            await self.db.commit()

            logger.info(f"用户 {current_user_id} 启用了批次任务: {task.name} (ID: {task_id})")
            return True

        except Exception as e:
            await self.db.rollback()
            logger.error(f"启用批次任务失败: {task.name} (ID: {task_id}), 错误: {str(e)}")
            raise

    async def disable_batch_task(self, task_id: int, current_user_id: int) -> bool:
        """禁用批次任务"""
        # 获取任务
        task = await self.repository.get(task_id)
        if not task:
            raise_not_found(f"批次任务不存在: {task_id}")

        try:
            # 更新任务状态为禁用
            update_data = ChaosBatchTaskUpdate(task_status="disabled")
            await self.repository.update_with_items(task_id, update_data, current_user_id)
            await self.db.commit()

            logger.info(f"用户 {current_user_id} 禁用了批次任务: {task.name} (ID: {task_id})")
            return True

        except Exception as e:
            await self.db.rollback()
            logger.error(f"禁用批次任务失败: {task.name} (ID: {task_id}), 错误: {str(e)}")
            raise


# 模块级别的函数，用于APScheduler调度
async def complete_batch_task_by_timeout(task_id: int, timeout_seconds: int) -> None:
    """timeout后自动完成批次任务 - 模块级函数，支持APScheduler序列化"""
    from app.database.session import get_async_session
    from app.repositories.chaos.chaos_batch_task_repository import ChaosBatchTaskRepository
    from datetime import datetime

    try:
        # 获取数据库会话
        async with get_async_session() as db:
            repository = ChaosBatchTaskRepository(db)

            # 获取任务
            task = await repository.get(task_id)
            if not task:
                logger.warning(f"批次任务 {task_id} 不存在，无法自动完成")
                return

            # 检查任务状态
            if task.status != "running":
                logger.info(f"批次任务 {task_id} 状态为 {task.status}，无需自动完成")
                return

            # 更新任务状态为完成
            task.update_status("completed", {
                "end_time": datetime.now().isoformat(),
                "auto_completed": True,
                "timeout_seconds": timeout_seconds
            })
            await db.commit()

            logger.info(f"批次任务 {task_id} 已自动完成（timeout: {timeout_seconds}秒）")

    except Exception as e:
        logger.error(f"自动完成批次任务 {task_id} 失败: {str(e)}")

# 常用模式和最佳实践

- 混沌测试任务状态管理最佳实践：运行状态(pending/running/completed/failed/cancelled)+任务状态(enabled/disabled)双重管理；启用/禁用通过Switch开关控制；调度器清理包含内存作业移除+数据库记录清理+配置状态更新三层清理
- 混沌测试任务自动完成最佳实践：使用APScheduler替代asyncio.sleep，支持服务重启后任务恢复；创建调度任务时使用函数对象而非字符串路径；包含完整的资源清理机制（任务停止/删除时清理调度器任务）
- 日志记录最佳实践：已为用户管理、环境管理、模型管理、混沌测试模块添加完整日志记录功能。包括增强日志工具类(BusinessLogger)、扩展BaseService基类自动记录CRUD操作、各模块添加业务操作日志、审计日志、性能监控日志。统一格式包含操作类型、用户信息、时间戳、操作结果、耗时统计。
- 修复ScheduleTaskService缺失批次任务调度方法：添加create_chaos_batch_schedule_task和update_chaos_batch_schedule_task方法，task_id格式为chaos_batch_task_{batch_task_id}，task_type为chaos_batch_execution；在schedule_tasks.py中添加execute_chaos_batch_task_by_config函数处理批次任务执行逻辑

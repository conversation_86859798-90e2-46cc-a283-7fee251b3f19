# 测试数据工厂平台 - 简化版设计方案

## 项目概述

### 目标定位
为中小型测试平台提供轻量级的测试数据生成解决方案，在保证核心功能完整性的同时，降低实施复杂度和维护成本。

### 核心价值
- **简单易用**: 降低学习成本，快速上手
- **功能实用**: 聚焦核心需求，避免过度设计
- **易于维护**: 简化架构，降低运维复杂度
- **快速交付**: 短周期迭代，快速验证价值

## 1. 简化架构设计

### 1.1 整体架构

```
┌─────────────────────────────────────────────────────────────┐
│                    前端界面层                                  │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │   模型设计器     │ │   任务管理面板   │ │   数据预览器     │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                    后端服务层                                  │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │   数据模型模块   │ │   生成引擎模块   │ │   任务管理模块   │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │   规则引擎模块   │ │   导出处理模块   │ │   连接器模块     │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                    数据存储层                                  │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │      Mysql      │ │      Redis      │ │   文件存储       │ │
│  │   (主数据库)     │ │    (缓存)       │ │   (临时文件)     │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 1.2 技术栈选择

**后端**:
- **框架**: FastAPI (保持与现有技术栈一致)
- **数据库**: Mysql + Redis
- **任务队列**: 集成的后台任务处理 (无需额外消息队列)
- **数据生成**: Python + Faker库 + 自定义生成器

**前端**:
- **框架**: Vue 3 + TypeScript
- **UI组件**: Element Plus
- **状态管理**: Pinia

## 2. 核心功能设计

### 2.1 数据模型管理

#### 模型定义结构
```json
{
  "id": "user_order_model",
  "name": "用户订单模型",
  "description": "简单的用户订单数据模型",
  "version": "1.0",
  "fields": [
    {
      "name": "user_id",
      "type": "string",
      "generator": "uuid",
      "description": "用户ID"
    },
    {
      "name": "user_name", 
      "type": "string",
      "generator": "name",
      "options": {
        "locale": "zh_CN"
      }
    },
    {
      "name": "age",
      "type": "integer", 
      "generator": "range",
      "options": {
        "min": 18,
        "max": 65
      }
    },
    {
      "name": "order_amount",
      "type": "decimal",
      "generator": "decimal",
      "options": {
        "min": 10.0,
        "max": 1000.0,
        "precision": 2
      }
    }
  ]
}
```

#### 支持的数据类型
- **基础类型**: string, integer, decimal, boolean, date, datetime
- **生成器类型**: uuid, name, phone, email, address, url, text
- **数值类型**: range, sequence, random
- **时间类型**: date_range, datetime_range, timestamp

#### 模型管理功能
- **模型CRUD**: 创建、编辑、删除、复制模型
- **模型验证**: 字段类型和生成器配置验证
- **模型预览**: 基于模型生成少量样例数据
- **模板库**: 提供常用的数据模型模板

### 2.2 数据生成引擎

#### 生成器设计

**内置生成器**:
```python
# 基础生成器
- UUIDGenerator: 生成UUID
- NameGenerator: 生成姓名 (支持中英文)
- PhoneGenerator: 生成手机号
- EmailGenerator: 生成邮箱地址
- AddressGenerator: 生成地址
- RangeGenerator: 生成数值范围
- SequenceGenerator: 生成递增序列
- DateRangeGenerator: 生成日期范围
- TextGenerator: 生成随机文本
- BooleanGenerator: 生成布尔值
```

**生成策略**:
- **小规模生成** (≤ 1万条): 内存生成，实时返回
- **中规模生成** (1万-10万条): 分批生成，显示进度
- **大规模生成** (> 10万条): 流式生成，后台任务

#### 数据一致性处理
- **唯一性约束**: 确保指定字段的唯一性
- **关联性约束**: 简单的字段间依赖关系
- **格式一致性**: 保证生成数据符合预定义格式

### 2.3 任务管理系统

#### 任务类型
```python
# 任务状态枚举
TaskStatus:
  - PENDING: 等待执行
  - RUNNING: 正在执行  
  - COMPLETED: 执行完成
  - FAILED: 执行失败
  - CANCELLED: 已取消

# 任务类型
TaskType:
  - INSTANT: 即时任务 (≤ 1000条)
  - BATCH: 批处理任务 (> 1000条)
```

#### 任务管理功能
- **任务创建**: 基于数据模型创建生成任务
- **任务监控**: 实时显示任务进度和状态
- **任务历史**: 查看历史任务记录和结果
- **任务重试**: 失败任务重新执行
- **任务取消**: 取消正在执行的任务

### 2.4 数据导出系统

#### 支持的导出格式
- **JSON**: 标准JSON格式，支持嵌套结构
- **CSV**: 逗号分隔值格式，适合表格数据
- **Excel**: .xlsx格式，支持多工作表
- **SQL**: 生成INSERT语句

#### 导出配置
- **格式选择**: 选择导出格式
- **字段映射**: 自定义字段名称映射
- **分页导出**: 大数据量分文件导出
- **压缩选项**: ZIP压缩下载

### 2.5 存储连接器(简化版)

#### 数据库连接器
- **MySQL连接器**: 直接插入MySQL数据库
- **PostgreSQL连接器**: 直接插入PostgreSQL数据库
- **文件导出**: 生成SQL文件供手动导入

#### 配置管理
- **连接配置**: 数据库连接参数管理
- **表映射**: 模型字段到数据库表字段的映射
- **批量插入**: 提高插入性能

## 3. 页面UI设计方案

### 3.1 整体布局设计

#### 主要页面结构
采用经典的中后台管理系统布局方案，包含三个主要区域：

**顶部导航栏**：
- 左侧显示系统Logo和主导航菜单
- 右侧显示用户信息、主题切换、消息通知等功能
- 固定在页面顶部，提供全局导航能力
- 高度约60px，保持与现有系统一致

**侧边栏**：
- 显示数据工厂模块的子菜单
- 支持展开/收缩功能，适应不同屏幕尺寸
- 宽度约240px（展开状态），64px（收缩状态）
- 提供清晰的功能分类和导航路径

**主内容区**：
- 显示当前页面的具体内容
- 自适应剩余空间，响应式布局
- 包含面包屑导航，明确当前位置
- 支持页面滚动，内容区域可独立滚动

#### 导航菜单设计
数据工厂模块包含四个主要功能模块：

**模型管理**：
- 图标：数据面板图标
- 功能：创建、编辑、管理数据模型
- 路径：/data-factory/models

**任务管理**：
- 图标：列表图标  
- 功能：查看、管理数据生成任务
- 路径：/data-factory/tasks

**数据预览**：
- 图标：查看图标
- 功能：预览和测试生成的数据
- 路径：/data-factory/preview

**模板库**：
- 图标：收藏图标
- 功能：管理和使用数据模型模板
- 路径：/data-factory/templates

### 3.2 数据模型管理页面

#### 模型列表页面 (`/data-factory/models`)

**页面布局结构**：

**页面头部**：
- 页面标题："数据模型管理"，配有说明文字："创建和管理测试数据模型，定义数据结构和生成规则"
- 右侧操作区域：包含"新建模型"主按钮（蓝色主题色）和"导入模板"次要按钮
- 整个头部采用卡片式设计，与页面背景有明显区分
- 头部高度约100px，给标题和说明文字足够的展示空间

**搜索筛选区域**：
- 水平表单布局，包含多个筛选条件
- 模型名称输入框：支持关键词搜索，带清除功能，宽度200px
- 创建时间选择器：日期范围选择，支持快捷日期选择，宽度220px
- 操作按钮：搜索按钮（主色调）和重置按钮（次要色调）
- 整个搜索区域采用卡片容器，与其他区域保持一致的间距

**模型展示区域**：
- 采用网格布局（Grid），每行最多显示3-4个卡片，根据屏幕宽度自适应
- 每个模型卡片包含完整的模型信息和快速操作功能
- 卡片间距20px，保证视觉舒适度
- 支持悬停效果，提供良好的交互反馈

**模型卡片设计**：
- 卡片头部：左侧显示模型名称（大字体，加粗）和描述信息（小字体，灰色）
- 右侧更多操作按钮：点击展开下拉菜单，包含编辑、复制、预览数据、生成数据、删除等操作
- 卡片主体：显示字段数量（带图标）、版本标签、创建时间等关键信息
- 卡片底部：左侧显示使用次数统计，右侧显示快速操作按钮（预览、生成数据）
- 整个卡片支持点击进入详情页面，但操作按钮点击事件需要阻止冒泡

**分页组件**：
- 位于页面底部中央位置
- 支持每页显示数量选择（12、24、48条记录）
- 包含总数显示、页码跳转、上一页/下一页导航
- 使用标准的分页组件样式，保持与系统一致性

**视觉设计规范**：

**整体布局**：
- 页面内边距：20px，为内容提供舒适的留白空间
- 页面背景色：使用系统统一的页面背景色变量，与整体风格保持一致
- 卡片间距：各个功能区域间保持24px的垂直间距，确保内容层次清晰

**页面头部样式**：
- 头部容器：使用弹性布局，左侧内容左对齐，右侧操作按钮右对齐
- 背景样式：白色背景，8px圆角，标准卡片阴影效果
- 标题样式：24px字体大小，600字重，主要文本颜色
- 描述文字：14px字体大小，次要文本颜色，与标题间距8px
- 按钮组：按钮间距12px，使用标准的按钮尺寸和颜色

**搜索区域样式**：
- 容器样式：白色背景，16px内边距，8px圆角，标准卡片阴影
- 表单布局：水平排列，表单项间自动间距
- 输入控件：统一高度，圆角边框，聚焦时显示主题色边框
- 按钮样式：搜索按钮使用主题色，重置按钮使用默认样式

**网格和卡片样式**：
- 网格布局：自适应列数，最小列宽320px，列间距和行间距均为20px
- 卡片基础样式：白色背景，1px浅色边框，8px圆角，20px内边距
- 悬停效果：边框变为主题色，阴影增强，向上位移2px，过渡时间0.3秒
- 卡片内容间距：头部、主体、底部区域间距16px

**文字和颜色规范**：
- 主标题：16px，600字重，主要文本颜色
- 描述文字：13px，400字重，次要文本颜色，支持两行省略
- 统计信息：13px，次要文本颜色，图标16px大小
- 时间信息：12px，次要文本颜色
- 分割线：使用浅色边框变量，1px实线

**响应式适配**：
- 移动端断点：768px以下
- 移动端调整：页面内边距减少到12px，网格改为单列布局
- 头部适配：改为垂直布局，文字居中对齐，按钮组居中显示
- 搜索表单：改为垂直布局，表单项宽度100%，间距调整为16px

#### 模型设计器页面 (`/data-factory/models/create` 或 `/data-factory/models/:id/edit`)

**页面布局结构**：

**顶部工具栏**：
- 左侧导航区域：返回按钮（带左箭头图标），分割线，页面标题（动态显示"新建模型"或"编辑模型"）
- 右侧操作区域：预览数据按钮（有字段时启用），保存模型主按钮（带加载状态）
- 工具栏固定在页面顶部，高度约60px，背景色与主内容区区分
- 按钮间距12px，使用标准的按钮样式和交互状态

**三栏式设计内容区域**：

**左侧字段库**（宽度约280px）：
- 标题区域：显示"字段库"标题和操作说明"拖拽字段到右侧模型区域"
- 字段分类展示：按照数据类型进行分类（基础类型、文本类型、数值类型、时间类型、特殊类型）
- 每个分类包含分类标题和字段项列表
- 字段项设计：左侧类型图标，右侧字段名称和描述，支持拖拽操作
- 字段项悬停效果：背景色变化，边框高亮，鼠标指针变为可拖拽状态

**中间模型设计区域**（自适应宽度）：
- 模型基本信息区域：包含模型名称、版本号、描述等基础信息表单
- 表单布局：模型名称和版本号使用两列布局，描述使用单列文本域
- 字段设计区域：包含区域标题"字段设计"和"添加字段"操作按钮
- 字段容器：支持拖拽接收，空状态时显示引导文字和空状态图标
- 字段配置项：每个字段包含字段头部（基本信息）和字段配置（详细设置）

**字段配置项详细设计**：
- 字段头部：类型图标，字段名称输入框，字段类型选择器，操作按钮组（上移、下移、删除）
- 字段配置：字段描述输入框，生成器选择器，生成器参数配置区域
- 生成器配置：根据选择的生成器类型动态显示相应的参数配置组件
- 配置项布局：使用栅格系统，生成器选择器占8列，参数配置占16列

**右侧实时预览区域**（宽度约300px）：
- 预览头部：显示"实时预览"标题和刷新按钮
- 预览内容区域：根据当前模型配置实时生成样例数据
- 空状态处理：当没有字段时显示空状态提示"添加字段后可查看预览数据"
- 数据表格：使用小尺寸表格展示生成的样例数据，支持超长文本省略
- 自动刷新：字段配置变更时自动触发预览数据更新

**交互设计特点**：

**拖拽交互**：
- 字段库中的字段项支持拖拽到模型设计区域
- 拖拽过程中显示拖拽提示和放置区域高亮
- 拖拽完成后自动添加字段到模型中，并生成唯一ID
- 支持字段在模型内的排序拖拽操作

**实时预览**：
- 字段添加、修改、删除时自动更新预览数据
- 生成器参数变更时实时反映在预览数据中
- 预览数据限制在5-10条，避免性能问题
- 支持手动刷新预览数据

**表单验证**：
- 模型名称必填验证，字段名称唯一性验证
- 生成器参数的格式和范围验证
- 实时验证反馈，错误状态和成功状态的视觉提示
- 保存前进行完整性检查，确保模型配置正确

**响应式适配**：
- 大屏幕（>1200px）：完整三栏布局，最佳体验
- 中等屏幕（768px-1200px）：字段库收缩，预览区域缩小
- 小屏幕（<768px）：改为纵向布局，字段库和预览区域可折叠
- 移动端优化：简化拖拽操作，增强触摸交互

### 3.3 任务管理页面

#### 任务列表页面 (`/data-factory/tasks`)

**页面布局结构**：

**页面头部**：
- 标题区域：显示"任务管理"主标题和"查看和管理数据生成任务，监控任务执行状态"说明
- 操作区域：包含刷新按钮和新建任务主按钮
- 头部样式：卡片式容器，与其他页面保持一致的视觉风格
- 按钮排列：刷新按钮在左，新建任务按钮在右，间距12px

**任务统计区域**：
- 四列统计卡片布局：正在执行、执行完成、执行失败、总任务数
- 每个统计卡片包含：状态图标（不同颜色）、数值显示、状态标签
- 卡片设计：白色背景，圆角边框，轻微阴影，悬停效果
- 图标颜色：运行中（蓝色旋转图标）、完成（绿色对勾）、失败（红色错误）、总数（灰色分析图标）
- 响应式布局：移动端改为两行显示，每行两个卡片

**搜索筛选区域**：
- 水平表单布局，包含任务状态、数据模型、创建时间三个筛选维度
- 任务状态选择器：包含等待执行、正在执行、执行完成、执行失败、已取消五个状态选项
- 数据模型选择器：动态加载系统中的数据模型列表，支持快速筛选
- 创建时间选择器：日期范围选择，支持快捷日期选项
- 操作按钮：搜索按钮（主题色）和重置按钮，位于表单右侧

**任务列表区域**：
- 表格形式展示任务列表，包含多列信息展示
- 支持多选功能，第一列为选择框列
- 任务ID列：固定宽度100px，显示任务的唯一标识
- 数据模型列：最小宽度150px，显示关联的数据模型名称
- 数据量列：宽度100px，右对齐显示，支持数值格式化（如：1,000）
- 状态列：宽度120px，使用彩色标签显示任务状态
- 进度列：宽度150px，使用进度条和百分比双重显示
- 创建时间列：宽度160px，格式化显示创建时间
- 耗时列：宽度100px，显示任务执行耗时（格式：1h 30m 45s）
- 操作列：固定在右侧，宽度200px，包含多个操作按钮

**任务状态和进度设计**：
- 状态标签颜色：等待执行（橙色）、正在执行（蓝色）、执行完成（绿色）、执行失败（红色）、已取消（灰色）
- 进度条设计：根据任务状态显示不同颜色，完成状态为绿色，失败状态为红色，进行中为蓝色
- 进度文字：进度条旁边显示具体的百分比数值
- 实时更新：正在执行的任务进度每5秒自动刷新

**操作按钮设计**：
- 详情按钮：所有状态的任务都显示，点击查看任务详细信息
- 下载按钮：仅完成状态的任务显示，支持下载生成的数据文件
- 取消按钮：仅正在执行的任务显示，红色警告样式
- 重试按钮：失败和已取消的任务显示，支持重新执行
- 删除按钮：所有状态都显示，红色警告样式，需要确认操作

**批量操作区域**：
- 当选择了任务时才显示，固定在表格下方
- 左侧显示选择数量信息："已选择 X 个任务"
- 右侧显示批量操作按钮：批量取消、批量删除
- 按钮样式：危险操作使用红色主题，需要二次确认

**分页组件**：
- 位于页面底部中央，包含完整的分页功能
- 显示总数、每页大小选择（20、50、100）、页码导航、跳转输入
- 分页样式与系统其他页面保持一致

**移动端适配**：
- 任务统计卡片：改为两行布局，每行显示两个卡片
- 搜索表单：改为垂直布局，每个筛选项占用全宽
- 任务列表：在移动端隐藏表格，改为卡片式布局
- 任务卡片包含：任务ID、模型名称、状态标签、进度条、创建时间、操作按钮
- 批量操作：在移动端简化为浮动操作按钮

### 3.4 数据生成配置对话框

**对话框设计结构**：

**对话框基本属性**：
- 标题："数据生成配置"
- 宽度：600px（桌面端），90%（移动端）
- 不允许点击遮罩关闭，确保用户完成配置
- 居中显示，带有标准的关闭按钮

**表单配置区域**：

**数据模型选择**：
- 下拉选择器，显示所有可用的数据模型
- 选项格式：模型名称为主标题，描述信息为副标题
- 选择后自动加载模型字段信息，用于后续配置
- 支持搜索过滤，快速定位目标模型

**生成数量设置**：
- 数字输入框，支持手动输入和步进调节
- 数值范围：1到1,000,000，步进值100
- 输入框宽度200px，右侧显示提示文字
- 智能提示："建议：≤1000条即时生成，>1000条后台处理"

**输出格式选择**：
- 单选按钮组，水平排列
- 四个选项：JSON、CSV、Excel、SQL
- 默认选中JSON格式
- 选项间距适中，易于点击选择

**输出方式配置**：
- 单选按钮组：文件下载、数据库插入
- 根据选择动态显示相应的配置区域
- 文件下载为默认选项，配置简单
- 数据库插入需要额外的连接配置

**数据库配置区域**（条件显示）：
- 仅在选择"数据库插入"时显示
- 数据库类型选择：MySQL、PostgreSQL两个选项
- 连接配置选择：下拉选择已配置的数据库连接，包含"配置新连接"快捷按钮
- 目标表名输入：手动输入要插入数据的表名，宽度300px

**高级选项配置**：
- 两个复选框选项，垂直排列
- "启用唯一性检查"：确保生成数据的唯一性约束
- "生成前预览数据"：在正式生成前查看样例数据

**数据预览区域**（条件显示）：
- 仅在启用预览选项且有预览数据时显示
- 标题："数据预览（前5条）"
- 使用小尺寸表格展示样例数据
- 表格列根据模型字段动态生成
- 列宽最小120px，支持超长文本省略提示

**对话框底部操作区域**：
- 三个按钮水平排列，右对齐
- 取消按钮：关闭对话框，不保存任何配置
- 预览数据按钮：仅在未启用自动预览时显示，点击生成预览数据
- 确认按钮：主按钮，动态文本显示
  - 小于等于1000条："立即生成"
  - 大于1000条："创建任务"
  - 支持加载状态，防止重复提交

**交互设计特点**：

**智能化配置**：
- 模型选择后自动更新相关配置选项
- 根据生成数量自动推荐处理方式
- 数据库类型选择后更新连接选项列表
- 表单验证实时反馈，减少提交错误

**用户体验优化**：
- 关键配置项提供帮助文本和建议
- 危险操作（如大数据量生成）给出明确提示
- 表单字段间有逻辑关联，简化用户操作
- 支持键盘快捷键操作（Enter确认，Esc取消）

**错误处理**：
- 表单验证规则：必填项检查、数值范围验证、格式验证
- 实时验证反馈，错误状态和成功状态视觉区分
- 提交前最终检查，确保所有必要信息完整
- 网络错误和业务错误的差异化提示

**响应式适配**：
- 移动端对话框宽度调整为90%，保持适当边距
- 表单标签宽度在移动端缩减为80px
- 输入控件在移动端使用全宽布局
- 按钮组在移动端改为垂直堆叠，增大点击区域

### 3.5 移动端适配

#### 响应式设计原则
**断点定义**：
- 移动端断点：768px以下
- 平板断点：768px-1024px之间  
- 桌面端断点：1024px以上

**模型管理页面移动端适配**：
- 页面头部：改为垂直布局，标题和操作按钮居中对齐，操作按钮组宽度100%
- 模型网格：由多列网格改为单列布局，卡片间距减少到16px
- 搜索表单：由水平布局改为垂直布局，所有输入控件宽度100%，表单项间距16px
- 卡片内容：保持原有信息层次，但调整间距和字体大小以适应小屏幕

**任务管理页面移动端适配**：
- 统计卡片：由四列改为两行两列布局，每个卡片下方增加16px间距
- 任务列表：隐藏表格组件，改为卡片式布局展示任务信息
- 任务卡片设计：
  - 卡片头部：任务ID和状态标签，字体加粗，颜色对比明显
  - 卡片内容：使用信息行布局，标签和数值左右对齐
  - 卡片底部：操作按钮右对齐，按钮间距8px
  - 每个卡片底部边距16px，保持视觉间隔

**模型设计器移动端适配**：
- 三栏布局改为垂直堆叠：字段库、模型设计区、预览区域
- 字段库：可折叠设计，默认收起状态，点击展开
- 模型设计区：占用主要空间，表单标签宽度减少到80px
- 预览区域：可折叠设计，在移动端默认隐藏
- 拖拽操作：简化为点击添加，减少触摸操作复杂度

**对话框移动端适配**：
- 对话框宽度：改为95%视口宽度，保持5%边距
- 表单标签：宽度减少到80px，保证输入区域充足
- 按钮组：改为垂直堆叠，每个按钮高度增加到48px，满足触摸要求
- 表格组件：在移动端使用简化的列表展示

### 3.6 主题适配

#### 暗色模式支持原则
**统一变量系统**：
- 所有组件使用系统定义的CSS变量，确保主题切换的一致性
- 背景色变量：主背景色、卡片背景色、悬停背景色
- 文本色变量：主要文本、次要文本、占位文本、禁用文本
- 边框色变量：浅色边框、基础边框、深色边框
- 阴影变量：适配暗色模式的阴影效果

**组件暗色适配**：
- 页面容器：使用暗色背景，调整内容区域的对比度
- 卡片组件：背景色变为深色，边框色调整为更亮的灰色
- 表格组件：行背景色、边框色、悬停效果的暗色适配
- 按钮组件：保持原有的色彩语义，调整背景和文字对比度
- 输入组件：背景色、边框色、占位文本的暗色调整

**特殊效果适配**：
- 悬停效果：在暗色模式下增强阴影效果，提高视觉反馈
- 阴影效果：调整阴影的透明度和扩散范围，适应暗色背景
- 边框高亮：主题色边框在暗色模式下的可见性增强
- 文本对比：确保所有文本在暗色背景下的可读性

**渐变和过渡**：
- 主题切换动画：平滑的颜色过渡，避免突兀的变化
- 组件状态过渡：悬停、聚焦、激活状态的平滑过渡
- 色彩渐变：在暗色模式下调整渐变的起止颜色

**无障碍支持**：
- 颜色对比度：确保文本和背景的对比度符合WCAG标准
- 色彩语义：保持色彩的语义含义（成功、警告、错误等）
- 键盘导航：在暗色模式下清晰的焦点指示器
- 屏幕阅读器：色彩信息的文本替代描述

## 4. MVP版本规划

### 3.1 MVP功能范围

#### 核心功能 (Must Have)
- [x] **数据模型定义**: 基础字段类型和生成器
- [x] **数据预览**: 生成少量样例数据预览
- [x] **即时生成**: 小规模数据即时生成 (≤ 1000条)
- [x] **JSON/CSV导出**: 基础格式导出功能
- [x] **任务历史**: 简单的任务记录查看

#### 扩展功能 (Should Have)
- [ ] **批处理任务**: 大规模数据后台生成
- [ ] **Excel导出**: 支持Excel格式导出  
- [ ] **数据库连接**: MySQL/PostgreSQL直接插入
- [ ] **模型模板**: 常用数据模型模板库
- [ ] **字段关联**: 简单的字段依赖关系

#### 高级功能 (Could Have)  
- [ ] **自定义生成器**: 用户自定义数据生成逻辑
- [ ] **定时任务**: 周期性数据生成
- [ ] **API接口**: 外部系统调用接口
- [ ] **数据统计**: 生成数据的统计分析

### 3.2 MVP技术实现

#### 简化的数据模型
```python
# 数据模型表结构
class DataModel:
    id: int
    name: str
    description: str
    fields_config: JSON  # 存储字段配置
    created_at: datetime
    updated_at: datetime
    created_by: str

# 生成任务表结构  
class GenerationTask:
    id: int
    model_id: int
    record_count: int
    status: str
    progress: int
    result_file: str  # 生成结果文件路径
    created_at: datetime
    completed_at: datetime
```

#### 核心API接口
```python
# 模型管理API
POST   /api/data-factory/models          # 创建模型
GET    /api/data-factory/models          # 获取模型列表
GET    /api/data-factory/models/{id}     # 获取模型详情
PUT    /api/data-factory/models/{id}     # 更新模型
DELETE /api/data-factory/models/{id}     # 删除模型

# 数据生成API
POST   /api/data-factory/models/{id}/preview    # 预览数据
POST   /api/data-factory/models/{id}/generate   # 生成数据
GET    /api/data-factory/tasks                  # 获取任务列表
GET    /api/data-factory/tasks/{id}             # 获取任务详情
DELETE /api/data-factory/tasks/{id}             # 取消/删除任务

# 数据导出API
GET    /api/data-factory/tasks/{id}/download    # 下载生成结果
```

## 5. 实施计划

### 4.1 开发阶段

#### 阶段一: MVP基础版本 (4-6周)
**目标**: 实现核心数据生成功能

**Week 1-2: 后端基础架构**
- [ ] 数据模型表结构设计
- [ ] 基础API框架搭建
- [ ] 数据模型CRUD接口
- [ ] 基础生成器实现 (UUID, Name, Range, Text)

**Week 3-4: 数据生成核心**
- [ ] 数据生成引擎开发
- [ ] 即时生成功能实现
- [ ] JSON/CSV导出功能
- [ ] 任务管理基础功能

**Week 5-6: 前端界面开发**
- [ ] 数据模型设计器界面
- [ ] 数据预览界面
- [ ] 任务管理界面
- [ ] 基础的用户交互功能

#### 阶段二: 功能完善版本 (3-4周)
**目标**: 完善核心功能，提升用户体验

**Week 7-8: 生成器扩展**
- [ ] 更多内置生成器 (Phone, Email, Address, Date)
- [ ] 生成器参数配置优化
- [ ] 数据一致性和唯一性处理
- [ ] 批处理任务支持

**Week 9-10: 导出和集成**
- [ ] Excel导出功能
- [ ] MySQL/PostgreSQL连接器
- [ ] 数据库直接插入功能
- [ ] 模型模板库

#### 阶段三: 优化增强版本 (2-3周)
**目标**: 性能优化和用户体验提升

**Week 11-12: 性能优化**
- [ ] 大数据量生成优化
- [ ] 任务进度显示优化
- [ ] 文件下载和压缩优化
- [ ] 错误处理和用户提示完善

**Week 13 (可选): 高级功能**
- [ ] 字段关联功能
- [ ] 自定义生成器框架
- [ ] API接口开放

### 4.2 测试和部署

#### 测试阶段 (1-2周)
- [ ] 单元测试编写
- [ ] 集成测试
- [ ] 性能测试
- [ ] 用户接受测试

#### 部署阶段 (1周)
- [ ] 生产环境部署
- [ ] 监控配置
- [ ] 用户培训文档
- [ ] 上线验证

## 6. 风险评估与应对

### 5.1 技术风险

#### 性能风险
**风险**: 大数据量生成可能导致系统卡顿
**应对**: 
- 实施分批生成策略
- 设置合理的数据量限制
- 优化生成算法

#### 数据一致性风险  
**风险**: 生成数据可能不满足业务约束
**应对**:
- 完善数据验证机制
- 提供数据预览功能
- 支持生成规则自定义

### 5.2 实施风险

#### 开发周期风险
**风险**: 功能复杂导致开发延期
**应对**:
- 严格控制MVP范围
- 采用敏捷开发模式
- 定期评估和调整计划

#### 用户接受度风险
**风险**: 功能不符合用户预期
**应对**:
- 早期用户调研和反馈
- 快速原型验证
- 迭代式功能完善

## 7. 成本估算

### 6.1 开发成本
- **人力成本**: 2-3个开发人员 × 3个月
- **基础设施**: MySQL + Redis (现有环境)
- **第三方依赖**: Faker库、ExcelJS等开源库

### 6.2 运维成本
- **存储成本**: 生成的数据文件存储
- **计算成本**: 数据生成过程的CPU和内存消耗
- **维护成本**: 日常bug修复和功能迭代

## 8. 成功指标

### 7.1 功能指标
- [ ] **模型创建成功率** > 95%
- [ ] **数据生成成功率** > 98%
- [ ] **导出功能成功率** > 99%
- [ ] **任务完成时间** < 预期时间的120%

### 7.2 性能指标
- [ ] **小规模生成** (1000条) < 10秒
- [ ] **中规模生成** (1万条) < 2分钟
- [ ] **大规模生成** (10万条) < 30分钟
- [ ] **系统可用性** > 99%

### 7.3 用户指标
- [ ] **用户满意度** > 4.0/5.0
- [ ] **功能使用率** > 80%
- [ ] **用户反馈响应时间** < 24小时

## 9. 后续规划

### 8.1 功能增强
- **自定义生成器**: 支持JavaScript自定义生成逻辑
- **API集成**: 提供REST API供外部系统调用
- **数据分析**: 生成数据的质量分析和统计
- **多租户支持**: 支持多团队独立使用

### 8.2 性能优化
- **分布式生成**: 支持多节点并行生成
- **智能缓存**: 提高重复生成场景的性能
- **资源管理**: 更精细的资源使用控制

### 8.3 生态建设
- **插件市场**: 社区贡献的生成器插件
- **模板共享**: 用户间共享数据模型模板
- **最佳实践**: 积累和分享使用经验

---

*本方案遵循最小化可行产品(MVP)原则，优先实现核心功能，在验证价值后逐步迭代完善。* 
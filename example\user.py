from sqlalchemy import Column, Integer, String, Boolean, DateTime, Enum, ForeignKey
from sqlalchemy.orm import relationship
from datetime import datetime
from enum import Enum as PyEnum
from app.core.db import Base

# 用户状态枚举
class UserStatus(str, PyEnum):
    ACTIVE = "active"       # 激活
    INACTIVE = "inactive"   # 未激活
    LOCKED = "locked"       # 锁定

# 用户表模型
class User(Base):
    __tablename__ = "sys_user"  # 表名（通常加前缀区分模块）
    
    id = Column(Integer, primary_key=True, index=True, comment="用户ID")
    username = Column(String(50), unique=True, nullable=False, index=True, comment="用户名")
    email = Column(String(100), unique=True, nullable=False, comment="邮箱")
    phone = Column(String(20), nullable=True, comment="手机号")
    password = Column(String(100), nullable=False, comment="加密后的密码")
    nickname = Column(String(50), nullable=True, comment="昵称")
    avatar = Column(String(255), nullable=True, comment="头像URL")
    status = Column(Enum(UserStatus), default=UserStatus.ACTIVE, comment="用户状态")
    is_superuser = Column(Boolean, default=False, comment="是否超级管理员")
    dept_id = Column(Integer, ForeignKey("sys_dept.id"), nullable=True, comment="部门ID")
    last_login_time = Column(DateTime, nullable=True, comment="最后登录时间")
    created_time = Column(DateTime, default=datetime.now, comment="创建时间")
    updated_time = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment="更新时间")
    
    # 关联关系（可选，根据业务需求）
    dept = relationship("Dept", back_populates="users")  # 关联部门
    roles = relationship("Role", secondary="sys_user_role", back_populates="users")  # 多对多关联角色

    def __repr__(self):
        return f"<User(id={self.id}, username={self.username}, email={self.email})>"
    
import { RoutesAlias } from '../routesAlias'
import { AppRouteRecord } from '@/types/router'

/**
 * 菜单列表、异步路由
 *
 * 支持两种模式:
 * 前端静态配置 - 直接使用本文件中定义的路由配置
 * 后端动态配置 - 后端返回菜单数据，前端解析生成路由
 *
 * 菜单标题（title）:
 * 可以是 i18n 的 key，也可以是字符串，比如：'用户列表'
 *
 * RoutesAlias.Layout 指向的是布局组件，后端返回的菜单数据中，component 字段需要指向 /index/index
 * 路由元数据（meta）：异步路由在 asyncRoutes 中配置，静态路由在 staticRoutes 中配置
 */
export const asyncRoutes: AppRouteRecord[] = [
  {
    name: 'Dashboard',
    path: '/dashboard',
    component: RoutesAlias.Layout,
    meta: {
      title: 'menus.dashboard.title',
      icon: '&#xe721;',
      roles: ['R_ADMIN', 'R_USER'] // 所有用户都可以访问仪表板
    },
    children: [
      {
        path: 'console',
        name: 'Console',
        component: RoutesAlias.Dashboard,
        meta: {
          title: 'menus.dashboard.console',
          keepAlive: false,
          fixedTab: true,
          roles: ['R_ADMIN', 'R_USER']
        }
      }
    ]
  },
  {
    path: '/system',
    name: 'System',
    component: RoutesAlias.Layout,
    meta: {
      title: 'menus.system.title',
      icon: '&#xe7b9;',
      roles: ['R_ADMIN'] // 系统管理只有管理员可以访问
    },
    children: [
      {
        path: 'user',
        name: 'User',
        component: RoutesAlias.User,
        meta: {
          title: 'menus.system.user',
          keepAlive: true,
          roles: ['R_ADMIN'] // 用户管理只有管理员可以访问
        }
      },
      {
        path: 'role',
        name: 'Role',
        component: RoutesAlias.Role,
        meta: {
          title: 'menus.system.role',
          keepAlive: true,
          roles: ['R_ADMIN'] // 角色管理只有管理员可以访问
        }
      },
      {
        path: 'user-center',
        name: 'UserCenter',
        component: RoutesAlias.UserCenter,
        meta: {
          title: 'menus.system.userCenter',
          isHide: true,
          keepAlive: true,
          isHideTab: true,
          roles: ['R_ADMIN', 'R_USER'] // 个人中心所有用户都可以访问
        }
      }
    ]
  },
  {
    path: '/config',
    name: 'Config',
    component: RoutesAlias.Layout,
    meta: {
      title: 'menus.config.title',
      icon: '&#xe766;',
      roles: ['R_ADMIN', 'R_USER'] // 通用配置只有管理员可以访问
    },
    children: [
      {
        path: 'environment',
        name: 'Environment',
        component: RoutesAlias.Environment,
        meta: {
          title: 'menus.config.environment',
          keepAlive: true,
          roles: ['R_ADMIN', 'R_USER'] // 环境管理只有管理员可以访问
        }
      },
      {
        path: 'model',
        name: 'ModelConfig',
        component: RoutesAlias.ModelConfig,
        meta: {
          title: 'menus.config.model',
          keepAlive: true,
          roles: ['R_ADMIN', 'R_USER'] // 模型管理只有管理员可以访问
        }
      }
    ]
  },
  {
    path: '/tools',
    name: 'Tools',
    component: RoutesAlias.Layout,
    meta: {
      title: 'menus.tools.title',
      icon: '&#xe6e8;',
      roles: ['R_ADMIN', 'R_USER'] // 所有用户都可以访问工具
    },
    children: [
      {
        path: 'common',
        name: 'CommonTools',
        component: '/tools/index',
        meta: {
          title: 'menus.tools.common',
          keepAlive: true,
          roles: ['R_ADMIN', 'R_USER']
        }
      }
    ]
  },
  {
    path: '/chaos',
    name: 'Chaos',
    component: RoutesAlias.Layout,
    meta: {
      title: 'menus.chaos.title',
      icon: '&#xe7a3;',
      roles: ['R_ADMIN', 'R_USER'] // 混沌测试模块所有用户都可以访问
    },
    children: [

      {
        path: 'tasks',
        name: 'ChaosTasks',
        component: RoutesAlias.ChaosTasks,
        meta: {
          title: 'menus.chaos.tasks',
          keepAlive: true,
          roles: ['R_ADMIN', 'R_USER']
        }
      },
      {
        path: 'tasks/create',
        name: 'ChaosTaskCreate',
        component: RoutesAlias.ChaosTaskFormCreate,
        meta: {
          title: 'menus.chaos.taskCreate',
          isHide: true,
          keepAlive: false,
          isHideTab: true,
          roles: ['R_ADMIN', 'R_USER']
        }
      },
      {
        path: 'tasks/create-batch',
        name: 'ChaosBatchTaskCreate',
        component: RoutesAlias.ChaosBatchTaskFormCreate,
        meta: {
          title: 'menus.chaos.batchTaskCreate',
          isHide: true,
          keepAlive: false,
          isHideTab: true,
          roles: ['R_ADMIN', 'R_USER']
        }
      },
      {
        path: 'tasks/:id',
        name: 'ChaosTaskDetail',
        component: RoutesAlias.ChaosTaskDetail,
        meta: {
          title: 'menus.chaos.taskDetail',
          isHide: true,
          keepAlive: false,
          isHideTab: true,
          roles: ['R_ADMIN', 'R_USER']
        }
      },
      {
        path: 'tasks/:id/edit',
        name: 'ChaosTaskEdit',
        component: RoutesAlias.ChaosTaskFormEdit,
        meta: {
          title: 'menus.chaos.taskEdit',
          isHide: true,
          keepAlive: false,
          isHideTab: true,
          roles: ['R_ADMIN', 'R_USER']
        }
      },
      {
        path: 'batch-tasks/:id',
        name: 'ChaosBatchTaskDetail',
        component: RoutesAlias.ChaosBatchTaskDetail,
        meta: {
          title: 'menus.chaos.batchTaskDetail',
          isHide: true,
          keepAlive: false,
          isHideTab: true,
          roles: ['R_ADMIN', 'R_USER']
        }
      },
      {
        path: 'batch-tasks/:id/edit',
        name: 'ChaosBatchTaskEdit',
        component: RoutesAlias.ChaosBatchTaskFormEdit,
        meta: {
          title: 'menus.chaos.batchTaskEdit',
          isHide: true,
          keepAlive: false,
          isHideTab: true,
          roles: ['R_ADMIN', 'R_USER']
        }
      },
      {
        path: 'scenarios',
        name: 'ChaosScenarios',
        component: RoutesAlias.ChaosScenarios,
        meta: {
          title: 'menus.chaos.scenarios',
          keepAlive: true,
          roles: ['R_ADMIN', 'R_USER']
        }
      },
      {
        path: 'scenarios/create',
        name: 'ChaosScenarioCreate',
        component: RoutesAlias.ChaosScenarioCreate,
        meta: {
          title: 'menus.chaos.scenarioCreate',
          isHide: true,
          keepAlive: false,
          isHideTab: true,
          roles: ['R_ADMIN', 'R_USER']
        }
      },
      {
        path: 'executions',
        name: 'ChaosExecutions',
        component: RoutesAlias.ChaosExecutions,
        meta: {
          title: 'menus.chaos.executions',
          keepAlive: true,
          roles: ['R_ADMIN', 'R_USER']
        }
      },
      {
        path: 'executions/:id',
        name: 'ChaosExecutionDetail',
        component: RoutesAlias.ChaosExecutionDetail,
        meta: {
          title: 'menus.chaos.executionDetail',
          isHide: true,
          keepAlive: false,
          isHideTab: true,
          roles: ['R_ADMIN', 'R_USER']
        }
      },
      {
        path: 'blade',
        name: 'ChaosBlade',
        component: RoutesAlias.ChaosBlade,
        meta: {
          title: 'menus.chaos.blade',
          keepAlive: true,
          roles: ['R_ADMIN', 'R_USER'] // ChaosBlade管理只有管理员可以访问
        }
      },
      {
        path: 'monitor',
        name: 'ChaosMonitor',
        component: RoutesAlias.ChaosMonitor,
        meta: {
          title: 'menus.chaos.monitor',
          keepAlive: true,
          roles: ['R_ADMIN', 'R_USER'] // 主机监控
        }
      }
    ]
  },
  {
    path: '/data-factory',
    name: 'DataFactory',
    component: RoutesAlias.Layout,
    meta: {
      title: '数据工厂',
      icon: '&#xe6b2;',
      roles: ['R_ADMIN', 'R_USER']
    },
    children: [
      {
        path: 'models',
        name: 'DataFactoryModels',
        component: RoutesAlias.DataFactoryModels,
        meta: {
          title: '数据模型',
          keepAlive: true,
          roles: ['R_ADMIN', 'R_USER']
        }
      },
      {
        path: 'models/create',
        name: 'DataFactoryModelCreate',
        component: RoutesAlias.DataFactoryModelFormCreate,
        meta: {
          title: '创建模型',
          isHide: true,
          keepAlive: false,
          isHideTab: true,
          roles: ['R_ADMIN', 'R_USER']
        }
      },
      {
        path: 'models/:id/edit',
        name: 'DataFactoryModelEdit',
        component: RoutesAlias.DataFactoryModelFormEdit,
        meta: {
          title: '编辑模型',
          isHide: true,
          keepAlive: false,
          isHideTab: true,
          roles: ['R_ADMIN', 'R_USER']
        }
      },
      {
        path: 'tasks',
        name: 'DataFactoryTasks',
        component: RoutesAlias.DataFactoryTasks,
        meta: {
          title: '任务管理',
          keepAlive: true,
          roles: ['R_ADMIN', 'R_USER']
        }
      },
      {
        path: 'tasks/create',
        name: 'DataFactoryTaskCreate',
        component: RoutesAlias.DataFactoryTaskCreate,
        meta: {
          title: '创建任务',
          isHide: true,
          keepAlive: false,
          isHideTab: true,
          roles: ['R_ADMIN', 'R_USER']
        }
      }
    ]
  }
]

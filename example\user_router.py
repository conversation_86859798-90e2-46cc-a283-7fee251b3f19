from fastapi import APIRouter, Depends, Path, Body, Query
from sqlalchemy.orm import Session
from typing import List, Any
from app.schemas.user_schema import (
    UserCreate, UserUpdate, UserQuery, UserResponse, UserPageResponse
)
from app.service.user_service import UserService
from app.api.v1.dependencies import get_user_service, get_db  # 依赖注入
from app.core.dependencies import get_current_user  # 认证依赖
from app.core.response import response_builder  # 统一响应工具
from app.models.user import User  # 用于类型提示

# 定义用户路由（前缀+标签）
user_router = APIRouter(
    prefix="/users",
    tags=["用户管理"],
    dependencies=[Depends(get_current_user)]  # 整个模块需要认证
)

@user_router.get("/{user_id}", response_model=UserResponse, summary="获取用户详情")
async def get_user(
    user_id: int = Path(..., ge=1, description="用户ID"),
    service: UserService = Depends(get_user_service)
):
    """获取指定ID的用户详情，包含角色信息"""
    user = await service.get_user(user_id)
    return response_builder.success(user)

@user_router.get("", response_model=UserPageResponse, summary="查询用户列表")
async def list_users(
    query: UserQuery = Depends(),  # 自动接收并校验查询参数
    service: UserService = Depends(get_user_service)
):
    """查询用户列表，支持关键词搜索、状态筛选、部门筛选和分页"""
    result = await service.list_users(query)
    return response_builder.success(result)

@user_router.post("", response_model=UserResponse, summary="创建用户")
async def create_user(
    user_in: UserCreate = Body(..., description="创建用户的信息"),
    service: UserService = Depends(get_user_service),
    current_user: User = Depends(get_current_user)  # 获取当前登录用户（操作人）
):
    """创建新用户，需要管理员权限（实际项目中可通过装饰器控制）"""
    # 此处可添加权限校验（如仅管理员可创建用户）
    user = await service.create_user(user_in, operator_id=current_user.id)
    return response_builder.success(user)

@user_router.put("/{user_id}", response_model=UserResponse, summary="更新用户")
async def update_user(
    user_id: int = Path(..., ge=1, description="用户ID"),
    user_in: UserUpdate = Body(..., description="更新的用户信息"),
    service: UserService = Depends(get_user_service),
    current_user: User = Depends(get_current_user)
):
    """更新用户信息，支持修改基本信息和密码"""
    user = await service.update_user(user_id, user_in, operator_id=current_user.id)
    return response_builder.success(user)

@user_router.delete("/{user_id}", summary="删除用户")
async def delete_user(
    user_id: int = Path(..., ge=1, description="用户ID"),
    service: UserService = Depends(get_user_service),
    current_user: User = Depends(get_current_user)
):
    """删除用户，不允许删除超级管理员和自己"""
    result = await service.delete_user(user_id, operator_id=current_user.id)
    return response_builder.success(message="删除成功" if result else "删除失败")
    
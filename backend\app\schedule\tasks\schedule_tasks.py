"""
定时任务相关功能
"""
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, Optional

from app.database.connection import get_db
from ..schedule_manager import add_interval_task, add_cron_task, add_date_task, get_job
from ..decorators import interval_task

logger = logging.getLogger(__name__)


@interval_task(
    task_id="sync_schedule_tasks",
    name="同步定时任务配置",
    minutes=1,
    task_type="schedule_sync",
    description="每1分钟检查并同步定时任务配置表到调度器",
    auto_register=True
)
async def sync_schedule_tasks():
    """从定时任务配置表同步任务到调度器"""
    try:
        async for db in get_db():
            from sqlalchemy import select, and_
            from sqlalchemy import text

            # 获取未来24小时内需要执行的定时任务配置
            current_time = datetime.now()
            end_time = current_time + timedelta(hours=24)

            # 查询定时任务配置表
            query = text("""
                SELECT task_id, task_name, task_type, schedule_type, schedule_config,
                       function_path, function_args, function_kwargs, is_active
                FROM schedule_task_config
                WHERE is_active = 1
                AND schedule_type IN ('date', 'interval', 'cron')
            """)

            result = await db.execute(query)
            schedule_configs = result.fetchall()

            synced_count = 0
            skipped_count = 0

            for config in schedule_configs:
                try:
                    task_id = config.task_id
                    schedule_config = json.loads(config.schedule_config) if isinstance(config.schedule_config, str) else config.schedule_config

                    # 检查任务是否已经在调度器中
                    existing_job = get_job(task_id)

                    if not existing_job:
                        # 根据调度类型添加到调度器
                        if config.schedule_type == 'date':
                            run_date = datetime.fromisoformat(schedule_config['run_date'])

                            # 检查是否过期
                            if run_date < current_time:
                                skipped_count += 1
                                logger.debug(f"跳过过期任务: {config.task_name} ({task_id})")
                                continue

                            # 添加单次执行任务
                            add_date_task(
                                task_id=task_id,
                                name=config.task_name,
                                func=execute_schedule_task,
                                run_date=run_date,
                                task_type=config.task_type,
                                description=f"执行定时任务: {config.task_name}",
                                args=(task_id,)
                            )
                            synced_count += 1
                            logger.debug(f"同步单次任务到调度器: {config.task_name} ({task_id})")

                        elif config.schedule_type == 'interval':
                            # 添加间隔执行任务
                            add_interval_task(
                                task_id=task_id,
                                name=config.task_name,
                                func=execute_schedule_task,
                                task_type=config.task_type,
                                description=f"执行定时任务: {config.task_name}",
                                args=(task_id,),
                                **schedule_config
                            )
                            synced_count += 1
                            logger.debug(f"同步间隔任务到调度器: {config.task_name} ({task_id})")

                        elif config.schedule_type == 'cron':
                            # 添加Cron任务
                            add_cron_task(
                                task_id=task_id,
                                name=config.task_name,
                                func=execute_schedule_task,
                                task_type=config.task_type,
                                description=f"执行定时任务: {config.task_name}",
                                args=(task_id,),
                                **schedule_config
                            )
                            synced_count += 1
                            logger.debug(f"同步Cron任务到调度器: {config.task_name} ({task_id})")

                except Exception as e:
                    logger.error(f"同步定时任务失败: {config.task_id}, 错误: {str(e)}")

            # 输出同步结果
            if synced_count > 0 or skipped_count > 0:
                logger.info(f"定时任务同步完成: 同步了 {synced_count} 个任务，跳过了 {skipped_count} 个过期任务")
            elif schedule_configs:
                logger.debug(f"检查了 {len(schedule_configs)} 个定时任务配置，无需同步")

            break

    except Exception as e:
        logger.error(f"同步定时任务失败: {str(e)}")


async def execute_schedule_task(task_id: str):
    """执行定时任务配置表中的任务"""
    history_id = None

    try:
        logger.info(f"开始执行定时任务: {task_id}")

        async for db in get_db():
            from sqlalchemy import text
            from app.services.schedule.schedule_history_service import ScheduleHistoryService

            # 查询任务配置
            query = text("""
                SELECT task_name, task_type, function_path, function_args, function_kwargs
                FROM schedule_task_config
                WHERE task_id = :task_id AND is_active = 1
            """)

            result = await db.execute(query, {"task_id": task_id})
            config = result.fetchone()

            if not config:
                logger.error(f"未找到任务配置: {task_id}")
                return

            # 创建执行历史记录
            history_service = ScheduleHistoryService(db)
            history = await history_service.create_execution_record(
                task_id=task_id,
                task_name=config.task_name,
                task_type=config.task_type,
                function_path=config.function_path,
                function_args=json.loads(config.function_args) if config.function_args else None,
                function_kwargs=json.loads(config.function_kwargs) if config.function_kwargs else None,
                trigger_type="scheduled"
            )
            history_id = history.id

            # 标记开始执行
            await history_service.mark_execution_started(history_id)

            # 根据任务类型执行相应的业务逻辑
            execution_result = None
            if config.task_type == "chaos_execution":
                # 执行混沌测试任务
                execution_result = await execute_chaos_task_by_config(config, db)
            else:
                logger.warning(f"未知的任务类型: {config.task_type}")
                raise ValueError(f"未知的任务类型: {config.task_type}")

            # 标记执行成功
            await history_service.mark_execution_completed(
                history_id=history_id,
                status="success",
                result=execution_result
            )

            logger.info(f"定时任务执行成功: {task_id}")
            break

    except Exception as e:
        logger.error(f"执行定时任务失败: {task_id}, 错误: {str(e)}")

        # 标记执行失败
        if history_id:
            try:
                async for db in get_db():
                    from app.services.schedule.schedule_history_service import ScheduleHistoryService
                    history_service = ScheduleHistoryService(db)
                    await history_service.mark_execution_completed(
                        history_id=history_id,
                        status="failed",
                        error_message=str(e),
                        exception_obj=e
                    )
                    break
            except Exception as history_error:
                logger.error(f"更新执行历史失败: {str(history_error)}")

        raise


async def execute_chaos_task_by_config(config, db):
    """根据配置执行混沌测试任务"""
    try:
        # 解析参数
        function_args = json.loads(config.function_args) if config.function_args else []
        function_kwargs = json.loads(config.function_kwargs) if config.function_kwargs else {}

        # 获取混沌测试任务ID
        chaos_task_id = function_args[0] if function_args else None
        if not chaos_task_id:
            logger.error("混沌测试任务ID未找到")
            return

        # 执行混沌测试
        from app.services.chaos.chaos_task_service import ChaosTaskService
        from app.schemas.chaos.chaos_task import ChaosTaskExecuteRequest

        task_service = ChaosTaskService(db)

        # 创建执行请求
        execute_request = ChaosTaskExecuteRequest(
            force=False,
            override_params=None,
            monitor_config=None
        )

        # 执行任务（使用系统用户ID）
        result = await task_service.execute_task(
            chaos_task_id,
            execute_request,
            current_user_id=1  # 系统自动执行
        )

        logger.info(f"混沌测试任务执行成功: {chaos_task_id}")

        # 返回详细的执行结果
        return {
            "chaos_task_id": chaos_task_id,
            "execution_result": result,
            "executed_at": datetime.now().isoformat(),
            "status": "success"
        }

    except Exception as e:
        logger.error(f"执行混沌测试任务失败: {str(e)}")
        raise


async def init_chaos_tasks():
    """初始化定时任务相关功能"""
    try:
        logger.info("初始化定时任务...")

        # 立即执行一次同步任务（会自动跳过过期任务）
        await sync_schedule_tasks()

        logger.info("定时任务初始化完成")

    except Exception as e:
        logger.error(f"初始化定时任务失败: {str(e)}")
        raise
